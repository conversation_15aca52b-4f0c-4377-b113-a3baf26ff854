# docker-compose.yml
version: '3.8'

services:
  web_crawler_app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: manus_crawler_service
    ports:
      - "8000:8000"
    volumes:
      - .:/app
      - chrome_profiles_data:/app/data/chrome_profiles
    environment:
      ADMIN_API_KEY: "your_super_secret_key_here"
      PYTHONUNBUFFERED: 1
      CHROME_PROFILE_BASE_PATH: /app/data/chrome_profiles
    # Để Playwright hiển thị UI (non-headless) từ Docker ra máy host,
    # cần cấu hình X11 forwarding hoặc dùng VNC. Việc này phức tạp và phụ thuộc vào HĐH host.
    # Cho development, chạy Playwright UI trực tiếp trên máy host thường dễ hơn.

volumes:
  chrome_profiles_data:
