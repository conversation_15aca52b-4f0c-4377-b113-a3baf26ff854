# Dockerfile
FROM mcr.microsoft.com/playwright/python:v1.44.0-jammy
# Sử dụng image ch<PERSON>h thức của Playwright để có sẵn trình duyệt và dependencies.
# Kiểm tra phiên bản mới nhất tại: https://playwright.dev/python/docs/docker

WORKDIR /app

# Sao chép file requirements trước để tận dụng Docker cache
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Sao chép toàn bộ mã nguồn ứng dụng
COPY . .

# Biến môi trường (ví dụ)
ENV PYTHONUNBUFFERED=1
ENV CHROME_PROFILE_BASE_PATH=/app/data/chrome_profiles

# Mở port cho FastAPI
EXPOSE 8000

# Lệnh để chạy ứng dụng FastAPI
CMD ["python", "run.py"]
