#!/usr/bin/env python3
"""
Test script for Interactive Chat functionality
"""

import asyncio
import json
from app.core.crawler import chat_with_manus_interactive

async def test_interactive_chat():
    """Test the interactive chat functionality"""
    
    print("🧪 Testing Interactive Chat with Manus...")
    
    # Test parameters
    test_message = "Hello, can you help me with a Python script?"
    test_task_url = "https://manus.im/app/test-task"  # This would be a real task URL
    test_profile = "manus_login_profile"  # Profile should be set up first
    
    # Mock websocket callback
    async def mock_websocket_callback(request_id: str, message: str):
        print(f"📡 WebSocket [{request_id[:8]}...]: {message}")
    
    print(f"💬 Message: {test_message}")
    print(f"📍 Task URL: {test_task_url}")
    print(f"👤 Profile: {test_profile}")
    print(f"🔄 Starting interactive chat test...\n")
    
    try:
        result = await chat_with_manus_interactive(
            message=test_message,
            task_url=test_task_url,
            profile_name=test_profile,
            headless=True,  # Set to False to see browser
            websocket_callback=mock_websocket_callback,
            request_id="test-request-123"
        )
        
        print("\n" + "="*50)
        print("🎉 INTERACTIVE CHAT TEST RESULT:")
        print("="*50)
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        if result.get("success"):
            print("\n✅ Interactive chat test PASSED!")
            
            if result.get("chat_response"):
                chat_resp = result["chat_response"]
                print(f"\n💬 Chat Exchange:")
                print(f"👤 User: {chat_resp.get('user_message', 'N/A')}")
                print(f"🤖 Manus: {chat_resp.get('manus_response', 'N/A')}")
                print(f"⏰ Time: {chat_resp.get('timestamp', 'N/A')}")
                
                if chat_resp.get('attachments'):
                    print(f"📎 Attachments: {len(chat_resp['attachments'])}")
                    for i, att in enumerate(chat_resp['attachments'], 1):
                        print(f"  {i}. {att.get('filename', 'Unknown')}")
            
            if result.get("updated_page_data"):
                page_data = result["updated_page_data"]
                print(f"\n📊 Updated Page Data:")
                print(f"📄 Title: {page_data.get('page_title', 'N/A')}")
                print(f"📋 Tasks: {len(page_data.get('tasks', []))}")
                print(f"💬 Messages: {len(page_data.get('chat_messages', []))}")
        else:
            print(f"\n❌ Interactive chat test FAILED!")
            print(f"Error: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"\n💥 Exception during test: {str(e)}")
        import traceback
        traceback.print_exc()

async def test_basic_functions():
    """Test basic functions without actual browser interaction"""
    
    print("\n🔧 Testing basic functions...")
    
    # Test timestamp function
    from app.core.crawler import get_current_timestamp
    timestamp = await get_current_timestamp()
    print(f"⏰ Current timestamp: {timestamp}")
    
    # Test selectors
    from app.core import selectors as sel
    print(f"🎯 Chat input selector: {sel.CHAT_INPUT_TEXTAREA_CSS}")
    print(f"🎯 Chat event selector: {sel.CHAT_EVENT_CONTAINER_CSS}")
    
    print("✅ Basic functions test completed!")

if __name__ == "__main__":
    print("🚀 Starting Manus Interactive Chat Tests...")
    print("="*60)
    
    # Run basic tests first
    asyncio.run(test_basic_functions())
    
    # Ask user if they want to run full interactive test
    print("\n" + "="*60)
    print("⚠️  FULL INTERACTIVE TEST REQUIREMENTS:")
    print("1. Chrome profile 'manus_login_profile' must be set up")
    print("2. Profile must be logged into Manus.im")
    print("3. Valid task URL must be provided")
    print("4. Internet connection required")
    
    response = input("\n🤔 Run full interactive test? (y/N): ").strip().lower()
    
    if response in ['y', 'yes']:
        print("\n🚀 Running full interactive chat test...")
        asyncio.run(test_interactive_chat())
    else:
        print("\n⏭️  Skipping full interactive test.")
    
    print("\n🎉 All tests completed!")
