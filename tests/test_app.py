#!/usr/bin/env python3
"""
Test script cho Manus Crawler Application
"""

import asyncio
import json
import os
import sys
from pathlib import Path

# Thêm thư mục hiện tại vào Python path
sys.path.insert(0, str(Path(__file__).parent))

async def test_html_parsing():
    """Test parsing HTML content từ file mẫu."""
    print("🧪 Testing HTML parsing...")

    try:
        from crawler import crawl_manus_page_content

        # Đọc file HTML mẫu
        html_file = "html-manus.example.html"
        if not os.path.exists(html_file):
            print(f"❌ File {html_file} không tồn tại!")
            return False

        with open(html_file, 'r', encoding='utf-8') as f:
            html_content = f.read()

        print(f"📄 Đã đọc file {html_file} ({len(html_content)} characters)")

        # Test crawl HTML
        result = await crawl_manus_page_content(
            html_content=html_content,
            headless=True
        )

        if result["success"]:
            print("✅ HTML parsing thành công!")
            data = result["data"]
            print(f"📊 Kết quả:")
            print(f"   - Page title: {data.get('page_title', 'N/A')}")
            print(f"   - Tasks found: {len(data.get('tasks', []))}")
            print(f"   - Chat messages: {len(data.get('chat_messages', []))}")
            print(f"   - Current task title: {data.get('current_task_title', 'N/A')}")

            # In chi tiết một số tasks
            tasks = data.get('tasks', [])
            if tasks:
                print(f"📝 Task đầu tiên:")
                first_task = tasks[0]
                for key, value in first_task.items():
                    print(f"     {key}: {value}")

            return True
        else:
            print(f"❌ HTML parsing thất bại: {result.get('message', 'Unknown error')}")
            return False

    except Exception as e:
        print(f"❌ Lỗi khi test HTML parsing: {str(e)}")
        return False

async def test_selectors():
    """Test các selectors được định nghĩa."""
    print("\n🎯 Testing selectors...")

    try:
        import manus_selectors as sel

        # Kiểm tra các selectors quan trọng
        important_selectors = [
            ("PAGE_TITLE_TAG", sel.PAGE_TITLE_TAG),
            ("TASK_ITEM_CONTAINER_CSS", sel.TASK_ITEM_CONTAINER_CSS),
            ("CHAT_EVENT_CONTAINER_CSS", sel.CHAT_EVENT_CONTAINER_CSS),
            ("NEW_TASK_BUTTON_PLAYWRIGHT", sel.NEW_TASK_BUTTON_PLAYWRIGHT),
        ]

        print("📋 Selectors được định nghĩa:")
        for name, selector in important_selectors:
            print(f"   ✓ {name}: {selector}")

        return True

    except Exception as e:
        print(f"❌ Lỗi khi test selectors: {str(e)}")
        return False

def test_file_structure():
    """Kiểm tra cấu trúc file dự án."""
    print("\n📁 Testing file structure...")

    required_files = [
        "main.py",
        "crawler.py",
        "manus_selectors.py",
        "requirements.txt",
        "Dockerfile",
        "docker-compose.yml",
        "templates/index.html",
        "html-manus.example.html",
        ".env",
        "README.md"
    ]

    missing_files = []
    existing_files = []

    for file_path in required_files:
        if os.path.exists(file_path):
            existing_files.append(file_path)
            print(f"   ✅ {file_path}")
        else:
            missing_files.append(file_path)
            print(f"   ❌ {file_path}")

    print(f"\n📊 Tổng kết:")
    print(f"   ✅ Files tồn tại: {len(existing_files)}")
    print(f"   ❌ Files thiếu: {len(missing_files)}")

    if missing_files:
        print(f"   📝 Files cần tạo: {', '.join(missing_files)}")
        return False

    return True

def test_environment():
    """Kiểm tra environment variables."""
    print("\n🌍 Testing environment...")

    try:
        from dotenv import load_dotenv
        load_dotenv()

        env_vars = [
            "ADMIN_API_KEY",
            "CHROME_PROFILE_BASE_PATH",
            "PYTHONUNBUFFERED"
        ]

        for var in env_vars:
            value = os.getenv(var)
            if value:
                print(f"   ✅ {var}: {value}")
            else:
                print(f"   ⚠️  {var}: Not set")

        return True

    except Exception as e:
        print(f"❌ Lỗi khi test environment: {str(e)}")
        return False

async def main():
    """Chạy tất cả tests."""
    print("🚀 Manus Crawler - Test Suite")
    print("=" * 50)

    tests = [
        ("File Structure", test_file_structure),
        ("Environment", test_environment),
        ("Selectors", test_selectors),
        ("HTML Parsing", test_html_parsing),
    ]

    results = []

    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name} test...")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test {test_name} failed with exception: {str(e)}")
            results.append((test_name, False))

    # Tổng kết
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS:")
    print("=" * 50)

    passed = 0
    failed = 0

    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
        else:
            failed += 1

    print(f"\n📈 Summary: {passed} passed, {failed} failed")

    if failed == 0:
        print("🎉 All tests passed! Ứng dụng sẵn sàng để chạy.")
        print("\n🚀 Để chạy ứng dụng:")
        print("   1. Cài đặt dependencies: pip install -r requirements.txt")
        print("   2. Cài đặt Playwright browsers: playwright install chromium")
        print("   3. Chạy ứng dụng: python main.py")
        print("   4. Hoặc dùng Docker: docker-compose up --build")
        print("   5. Truy cập: http://localhost:8000/ui")
    else:
        print("⚠️  Một số tests thất bại. Vui lòng kiểm tra và sửa lỗi.")

    return failed == 0

if __name__ == "__main__":
    asyncio.run(main())
