#!/usr/bin/env python3
"""
Test API endpoints
"""

import asyncio
import httpx
import pytest
from fastapi.testclient import TestClient

# Import app từ cấu trúc mới
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.main import app

client = TestClient(app)

def test_root():
    """Test root endpoint."""
    response = client.get("/")
    assert response.status_code == 200
    assert "Manus Crawler API" in response.text

def test_health():
    """Test health endpoint."""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"

def test_ui_page():
    """Test UI page endpoint."""
    response = client.get("/ui")
    # Có thể 404 nếu template không tồn tại, nhưng endpoint phải hoạt động
    assert response.status_code in [200, 404]

def test_admin_page():
    """Test admin page endpoint."""
    response = client.get("/admin")
    # <PERSON><PERSON> thể 404 nếu template không tồn tại, nhưng endpoint phải hoạt động
    assert response.status_code in [200, 404]

def test_crawl_html():
    """Test HTML crawl endpoint."""
    test_data = {
        "html_content": "<html><head><title>Test</title></head><body><h1>Test</h1></body></html>",
        "headless": True
    }
    
    response = client.post("/crawl-html/", json=test_data)
    assert response.status_code == 200
    data = response.json()
    assert "success" in data

def test_admin_endpoints_without_api_key():
    """Test admin endpoints without API key."""
    # Test list profiles without API key
    response = client.get("/admin/list-profiles/")
    assert response.status_code == 401
    
    # Test setup profile without API key
    response = client.post("/admin/setup-chrome-profile/", json={
        "profile_name": "test",
        "url": "https://example.com"
    })
    assert response.status_code == 401

def test_admin_endpoints_with_api_key():
    """Test admin endpoints with API key."""
    headers = {"X-API-KEY": "your_super_secret_key_here"}
    
    # Test list profiles with API key
    response = client.get("/admin/list-profiles/", headers=headers)
    assert response.status_code == 200
    data = response.json()
    assert "success" in data
    assert "profiles" in data

if __name__ == "__main__":
    pytest.main([__file__])
