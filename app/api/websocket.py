"""
WebSocket handlers for real-time communication
"""

import asyncio
from typing import Dict, List, Any, Callable, Awaitable
from fastapi import <PERSON><PERSON>outer, WebSocket, WebSocketDisconnect

from ..models.schemas import WebSocketMessage, CrawlUrlRequestWithId, CrawlHtmlRequestWithId
from ..core.crawler import crawl_manus_page_content

router = APIRouter()

# WebSocket Connection Manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, List[WebSocket]] = {}

    async def connect(self, websocket: WebSocket, request_id: str):
        await websocket.accept()
        if request_id not in self.active_connections:
            self.active_connections[request_id] = []
        self.active_connections[request_id].append(websocket)

    def disconnect(self, websocket: WebSocket, request_id: str):
        if request_id in self.active_connections:
            if websocket in self.active_connections[request_id]:
                self.active_connections[request_id].remove(websocket)
            if not self.active_connections[request_id]:
                del self.active_connections[request_id]

    async def send_to_request_id(self, request_id: str, message: str):
        if request_id in self.active_connections:
            websocket_message = WebSocketMessage(type="progress", message=message)
            message_json = websocket_message.model_dump_json()
            
            disconnected_websockets = []
            for websocket in self.active_connections[request_id]:
                try:
                    await websocket.send_text(message_json)
                except:
                    disconnected_websockets.append(websocket)
            
            # Xóa các websocket đã disconnect
            for ws in disconnected_websockets:
                self.disconnect(ws, request_id)

    async def send_data_to_request_id(self, request_id: str, data: Dict[str, Any]):
        if request_id in self.active_connections:
            websocket_message = WebSocketMessage(type="data", data=data)
            message_json = websocket_message.model_dump_json()
            
            disconnected_websockets = []
            for websocket in self.active_connections[request_id]:
                try:
                    await websocket.send_text(message_json)
                except:
                    disconnected_websockets.append(websocket)
            
            # Xóa các websocket đã disconnect
            for ws in disconnected_websockets:
                self.disconnect(ws, request_id)

    async def send_error_to_request_id(self, request_id: str, error_message: str):
        if request_id in self.active_connections:
            websocket_message = WebSocketMessage(type="error", message=error_message)
            message_json = websocket_message.model_dump_json()
            
            disconnected_websockets = []
            for websocket in self.active_connections[request_id]:
                try:
                    await websocket.send_text(message_json)
                except:
                    disconnected_websockets.append(websocket)
            
            # Xóa các websocket đã disconnect
            for ws in disconnected_websockets:
                self.disconnect(ws, request_id)

# Global connection manager
manager = ConnectionManager()

@router.websocket("/ws/crawl-status/{request_id}")
async def websocket_endpoint(websocket: WebSocket, request_id: str):
    await manager.connect(websocket, request_id)
    try:
        while True:
            # Giữ kết nối mở
            await websocket.receive_text()
    except WebSocketDisconnect:
        manager.disconnect(websocket, request_id)

# Realtime crawl endpoints
@router.post("/crawl-url-realtime/")
async def crawl_url_realtime(request: CrawlUrlRequestWithId):
    """Crawl URL với cập nhật realtime qua WebSocket."""
    
    async def websocket_callback(request_id: str, message: str):
        await manager.send_to_request_id(request_id, message)
    
    # Chạy crawl trong background task
    async def run_crawl():
        result = await crawl_manus_page_content(
            url=request.url,
            profile_name=request.profile_name,
            use_system_profile=request.use_system_profile,
            headless=request.headless,
            websocket_callback=websocket_callback,
            request_id=request.request_id
        )
        
        if result["success"]:
            await manager.send_data_to_request_id(request.request_id, result["data"])
        else:
            await manager.send_error_to_request_id(request.request_id, result.get("message", "Unknown error"))
        
        return result
    
    # Khởi chạy task trong background
    asyncio.create_task(run_crawl())
    
    return {"message": "Crawl started", "request_id": request.request_id}

@router.post("/crawl-html-realtime/")
async def crawl_html_realtime(request: CrawlHtmlRequestWithId):
    """Parse HTML content với cập nhật realtime qua WebSocket."""
    
    async def websocket_callback(request_id: str, message: str):
        await manager.send_to_request_id(request_id, message)
    
    # Chạy crawl trong background task
    async def run_crawl():
        result = await crawl_manus_page_content(
            html_content=request.html_content,
            profile_name=request.profile_name,
            use_system_profile=request.use_system_profile,
            headless=request.headless,
            websocket_callback=websocket_callback,
            request_id=request.request_id
        )
        
        if result["success"]:
            await manager.send_data_to_request_id(request.request_id, result["data"])
        else:
            await manager.send_error_to_request_id(request.request_id, result.get("message", "Unknown error"))
        
        return result
    
    # Khởi chạy task trong background
    asyncio.create_task(run_crawl())
    
    return {"message": "HTML parsing started", "request_id": request.request_id}
