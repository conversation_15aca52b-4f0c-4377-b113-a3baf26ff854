{"generalPageElements": {"pageTitleTag": "title"}, "sidebar": {"taskItemContainerCss": "div.flex.flex-col.overflow-auto.pt-2.pb-5 > div.px-2 > div.group.flex.h-14", "taskItemIconImgCss": "img", "taskItemTitleSpanCss": "span.truncate.text-sm.font-medium[title]", "taskItemTimestampCss": "span.text-\\[var\\(--text-tertiary\\)\\].text-xs.whitespace-nowrap", "taskItemPreviewSpanCss": "span.min-w-0.flex-1.truncate.text-xs[title]", "newTaskButtonPlaywright": "button:has-text('New task')"}, "mainContentArea": {"currentTaskTitleMainCss": "div.sticky.top-0 div.text-\\[var\\(--text-primary\\)\\].text-lg.font-medium span.whitespace-nowrap.text-ellipsis.overflow-hidden", "chatEventContainerCss": "div[data-event-id]", "userMessageTextCss": "div.items-end div.p-3.ltr\\:rounded-br-none span.u-break-words", "manusMessageContentProseCss": "div.prose, div.mb-4.last\\:mb-0.whitespace-pre-wrap.u-break-words", "messageTimestampCss": "div.float-right.text-\\[12px\\]", "attachmentInMessageContainerCss": "div.rounded-\\[10px\\].bg-\\[var\\(--fill-tsp-white-main\\)\\].group\\/attach", "attachmentInMessageFilenameCss": "div.rounded-\\[10px\\].bg-\\[var\\(--fill-tsp-white-main\\)\\].group\\/attach div.text-sm.text-\\[var\\(--text-primary\\)\\]", "attachmentInMessageDetailsCss": "div.rounded-\\[10px\\].bg-\\[var\\(--fill-tsp-white-main\\)\\].group\\/attach div.text-xs.text-\\[var\\(--text-tertiary\\)\\]"}, "footerSidebar": {"userAvatarImgCss": "footer img.w-full.h-full.object-cover", "userNameSpanCss": "footer span.text-sm.leading-5.font-medium.text-\\[var\\(--text-primary\\)\\]"}, "computerPreviewArea": {"containerId": "#computer", "filenameCss": "#computer div.text-\\[var\\(--text-tertiary\\)\\]", "monacoLinesCss": "#computer div.monaco-editor div.view-lines"}, "inputArea": {"chatInputTextareaCss": "textarea[placeholder='Send message to <PERSON><PERSON>']"}, "loginPage": {"signInWithGoogleButtonXPath": "//button[.//div[contains(text(),'Google')]]", "signInWithGoogleButtonCss": "button:has-text('Sign in with Google')", "googleEmailInputSelector": "input[type='email']", "googlePasswordInputSelector": "input[type='password']", "googleNextButtonSelector": "button:has-text('Next')"}}