import os
import asyncio
import platform
from pathlib import Path
from typing import Optional, Dict, Any, Callable, Awaitable
from playwright.async_api import async_<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Page
from . import selectors as sel
from .config import settings
from .message_classifier import ManusMessageClassifier

def get_system_chrome_user_data_dir() -> Optional[str]:
    """L<PERSON><PERSON> đường dẫn User Data Directory của Chrome hệ thống."""
    system = platform.system()
    home = Path.home()

    if system == "Windows":
        return str(home / "AppData" / "Local" / "Google" / "Chrome" / "User Data")
    elif system == "Darwin":  # macOS
        return str(home / "Library" / "Application Support" / "Google" / "Chrome")
    elif system == "Linux":
        return str(home / ".config" / "google-chrome")
    else:
        return None

def cleanup_profile_locks(user_data_dir: str):
    """Xóa các lock files để tránh lỗi SingletonLock."""
    if user_data_dir and os.path.exists(user_data_dir):
        lock_files = [
            os.path.join(user_data_dir, "SingletonLock"),
            os.path.join(user_data_dir, "lockfile"),
            os.path.join(user_data_dir, "SingletonSocket"),
            os.path.join(user_data_dir, "SingletonCookie")
        ]

        for lock_file in lock_files:
            try:
                if os.path.exists(lock_file):
                    os.remove(lock_file)
            except Exception:
                pass  # Ignore errors when removing lock files

def kill_chrome_processes_for_profile(profile_name: str):
    """Kill Chrome processes sử dụng profile cụ thể."""
    import subprocess

    try:
        # Kill Chrome processes với profile name
        subprocess.run([
            "pkill", "-f", f"chrome.*{profile_name}"
        ], capture_output=True)

        subprocess.run([
            "pkill", "-f", f"Chromium.*{profile_name}"
        ], capture_output=True)

        # Đợi một chút để processes terminate
        import time
        time.sleep(0.5)

    except Exception:
        pass  # Ignore errors when killing processes

async def launch_browser_with_profile(
    profile_name: Optional[str] = None,
    use_system_profile: bool = False,
    headless: bool = True
) -> tuple[Browser, BrowserContext]:
    """
    Khởi chạy browser với profile được chỉ định.

    Args:
        profile_name: Tên profile tùy chỉnh
        use_system_profile: Sử dụng profile Chrome hệ thống
        headless: Chạy ở chế độ headless

    Returns:
        Tuple của (browser, context)
    """
    playwright = await async_playwright().start()

    if use_system_profile:
        user_data_dir = get_system_chrome_user_data_dir()
        if not user_data_dir or not os.path.exists(user_data_dir):
            raise ValueError("Không tìm thấy Chrome profile hệ thống")
    elif profile_name:
        user_data_dir = os.path.join(settings.CHROME_PROFILE_BASE_PATH, profile_name)
        os.makedirs(user_data_dir, exist_ok=True)
    else:
        user_data_dir = None

    # Cleanup lock files và kill processes trước khi launch
    if user_data_dir and profile_name:
        kill_chrome_processes_for_profile(profile_name)
        cleanup_profile_locks(user_data_dir)

    try:
        browser = await playwright.chromium.launch_persistent_context(
            user_data_dir=user_data_dir,
            headless=headless,
            args=[
                "--no-sandbox",
                "--disable-blink-features=AutomationControlled",
                "--disable-web-security",
                "--disable-features=VizDisplayCompositor",
                "--disable-dev-shm-usage",
                "--disable-extensions",
                "--no-first-run",
                "--disable-default-apps"
            ]
        )
    except Exception as e:
        # Nếu vẫn lỗi, thử cleanup lần nữa và retry
        if "SingletonLock" in str(e) or "ProcessSingleton" in str(e):
            if user_data_dir and profile_name:
                kill_chrome_processes_for_profile(profile_name)
                cleanup_profile_locks(user_data_dir)
                # Đợi một chút rồi thử lại
                await asyncio.sleep(2)
                browser = await playwright.chromium.launch_persistent_context(
                    user_data_dir=user_data_dir,
                    headless=headless,
                    args=[
                        "--no-sandbox",
                        "--disable-blink-features=AutomationControlled",
                        "--disable-web-security",
                        "--disable-features=VizDisplayCompositor",
                        "--disable-dev-shm-usage",
                        "--disable-extensions",
                        "--no-first-run",
                        "--disable-default-apps"
                    ]
                )
            else:
                raise e
        else:
            raise e

    return playwright, browser

async def setup_chrome_profile_interactive(profile_name: str, url: str = "https://manus.im/") -> Dict[str, Any]:
    """
    Thiết lập Chrome profile bằng cách mở trình duyệt non-headless
    để người dùng đăng nhập thủ công.

    Args:
        profile_name: Tên profile để tạo/sử dụng
        url: URL để mở (mặc định là Manus.im)

    Returns:
        Dictionary chứa thông tin về việc setup profile
    """
    try:
        playwright, context = await launch_browser_with_profile(
            profile_name=profile_name,
            headless=False  # Non-headless để người dùng tương tác
        )

        page = await context.new_page()
        await page.goto(url)

        # Chờ người dùng đăng nhập (có thể điều chỉnh thời gian)
        await page.wait_for_timeout(60000)  # 60 giây

        # Kiểm tra xem có đăng nhập thành công không
        current_url = page.url

        await context.close()
        await playwright.stop()

        return {
            "success": True,
            "profile_name": profile_name,
            "final_url": current_url,
            "message": f"Profile '{profile_name}' đã được thiết lập"
        }

    except Exception as e:
        return {
            "success": False,
            "profile_name": profile_name,
            "error": str(e),
            "message": f"Lỗi khi thiết lập profile '{profile_name}': {str(e)}"
        }

async def crawl_manus_page_content(
    html_content: Optional[str] = None,
    url: Optional[str] = None,
    profile_name: Optional[str] = None,
    use_system_profile: bool = False,
    headless: bool = True,
    websocket_callback: Optional[Callable[[str, str], Awaitable[None]]] = None,
    request_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Crawl nội dung từ trang Manus.im hoặc parse HTML tĩnh.

    Args:
        html_content: Nội dung HTML tĩnh để parse
        url: URL để crawl (nếu không có html_content)
        profile_name: Tên profile Chrome để sử dụng
        use_system_profile: Sử dụng profile Chrome hệ thống
        headless: Chạy ở chế độ headless
        websocket_callback: Callback để gửi cập nhật realtime
        request_id: ID của request để tracking

    Returns:
        Dictionary chứa dữ liệu đã crawl
    """

    async def send_progress(message: str):
        """Gửi thông báo tiến trình qua WebSocket nếu có callback."""
        if websocket_callback and request_id:
            await websocket_callback(request_id, message)

    try:
        await send_progress("Bắt đầu khởi chạy trình duyệt...")

        playwright, context = await launch_browser_with_profile(
            profile_name=profile_name,
            use_system_profile=use_system_profile,
            headless=headless
        )

        page = await context.new_page()

        if html_content:
            await send_progress("Đang load HTML content...")
            await page.set_content(html_content)
        elif url:
            await send_progress(f"Đang truy cập URL: {url}")
            await page.goto(url, wait_until="networkidle")
        else:
            raise ValueError("Cần cung cấp html_content hoặc url")

        await send_progress("Đang trích xuất dữ liệu...")

        # Trích xuất dữ liệu theo selectors
        data = {
            "page_title": "",
            "tasks": [],
            "current_task_title": "",
            "chat_messages": [],
            "footer_user": {},
            "profile_status": {
                "profile_name": profile_name,
                "use_system_profile": use_system_profile,
                "headless": headless
            }
        }

        # Lấy title trang
        try:
            data["page_title"] = await page.title()
        except:
            pass

        # Lấy danh sách tasks từ sidebar
        await send_progress("Đang lấy danh sách tasks...")
        try:
            task_containers = await page.query_selector_all(sel.TASK_ITEM_CONTAINER_CSS)
            for container in task_containers:
                task_data = {}

                # Icon
                try:
                    icon_img = await container.query_selector(sel.TASK_ITEM_ICON_IMG_CSS)
                    if icon_img:
                        task_data["icon_src"] = await icon_img.get_attribute("src")
                except:
                    pass

                # Title
                try:
                    title_span = await container.query_selector(sel.TASK_ITEM_TITLE_SPAN_CSS)
                    if title_span:
                        task_data["title"] = await title_span.get_attribute("title")
                        task_data["title_text"] = await title_span.inner_text()
                except:
                    pass

                # Timestamp
                try:
                    timestamp_span = await container.query_selector(sel.TASK_ITEM_TIMESTAMP_CSS)
                    if timestamp_span:
                        task_data["timestamp"] = await timestamp_span.inner_text()
                except:
                    pass

                # Preview
                try:
                    preview_span = await container.query_selector(sel.TASK_ITEM_PREVIEW_SPAN_CSS)
                    if preview_span:
                        task_data["preview"] = await preview_span.get_attribute("title")
                except:
                    pass

                if task_data:
                    data["tasks"].append(task_data)
        except Exception as e:
            await send_progress(f"Lỗi khi lấy tasks: {str(e)}")

        # Lấy tiêu đề task hiện tại
        try:
            current_title_elem = await page.query_selector(sel.CURRENT_TASK_TITLE_MAIN_CSS)
            if current_title_elem:
                data["current_task_title"] = await current_title_elem.inner_text()
        except:
            pass

        # Lấy tin nhắn chat
        await send_progress("Đang lấy tin nhắn chat...")
        try:
            chat_events = await page.query_selector_all(sel.CHAT_EVENT_CONTAINER_CSS)
            for event in chat_events:
                message_data = {
                    "event_id": await event.get_attribute("data-event-id")
                }

                # Tin nhắn user
                try:
                    user_msg = await event.query_selector(sel.USER_MESSAGE_TEXT_CSS)
                    if user_msg:
                        message_data["user_message"] = await user_msg.inner_text()
                        message_data["type"] = "user"
                except:
                    pass

                # Tin nhắn Manus
                try:
                    manus_msg = await event.query_selector(sel.MANUS_MESSAGE_CONTENT_PROSE_CSS)
                    if manus_msg:
                        manus_text = await manus_msg.inner_text()
                        manus_html = await manus_msg.inner_html()

                        message_data["manus_message"] = manus_text
                        message_data["manus_html"] = manus_html
                        message_data["type"] = "manus"

                        # Phân loại message type cho Manus
                        try:
                            classification = ManusMessageClassifier.classify_message_type(
                                manus_html=manus_html,
                                manus_text=manus_text
                            )
                            message_data["message_subtype"] = classification.get("message_subtype", "text")
                            # message_data["content_analysis"] = classification.get("content_analysis", {})
                        except Exception as classify_error:
                            # Fallback nếu phân loại thất bại
                            message_data["message_subtype"] = "text"
                            message_data["content_analysis"] = {"error": str(classify_error)}
                except:
                    pass

                # Timestamp
                try:
                    timestamp_elem = await event.query_selector(sel.MESSAGE_TIMESTAMP_CSS)
                    if timestamp_elem:
                        message_data["timestamp"] = await timestamp_elem.inner_text()
                except:
                    pass

                if "user_message" in message_data or "manus_message" in message_data:
                    data["chat_messages"].append(message_data)
        except Exception as e:
            await send_progress(f"Lỗi khi lấy chat messages: {str(e)}")

        # Lấy thông tin user ở footer
        try:
            footer_avatar = await page.query_selector(sel.FOOTER_USER_AVATAR_IMG_CSS)
            footer_name = await page.query_selector(sel.FOOTER_USER_NAME_SPAN_CSS)

            if footer_avatar or footer_name:
                data["footer_user"] = {
                    "avatar_src": await footer_avatar.get_attribute("src") if footer_avatar else None,
                    "name": await footer_name.inner_text() if footer_name else None
                }
        except:
            pass

        await send_progress("Hoàn thành crawl dữ liệu!")

        await context.close()
        await playwright.stop()

        return {
            "success": True,
            "data": data,
            "message": "Crawl thành công"
        }

    except Exception as e:
        await send_progress(f"Lỗi: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "message": f"Lỗi khi crawl: {str(e)}"
        }

async def chat_with_manus_interactive(
    message: str,
    task_url: str,
    profile_name: Optional[str] = None,
    headless: bool = True,
    websocket_callback: Optional[Callable[[str, str], Awaitable[None]]] = None,
    request_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Gửi message lên Manus.im và crawl response realtime

    Args:
        message: Message to send to Manus
        task_url: Manus task URL (e.g., https://manus.im/app/task-id)
        profile_name: Chrome profile name for authentication
        headless: Run in headless mode
        websocket_callback: Callback for realtime updates
        request_id: Request ID for WebSocket tracking

    Returns:
        Dict containing chat response and updated page data
    """

    async def send_progress(msg: str):
        """Gửi thông báo tiến trình qua WebSocket nếu có callback."""
        if websocket_callback and request_id:
            await websocket_callback(request_id, f"💬 {msg}")

    try:
        await send_progress("Initializing chat session...")

        # Launch browser with profile
        await send_progress("Starting browser with profile...")
        playwright, context = await launch_browser_with_profile(
            profile_name=profile_name,
            headless=headless
        )

        page = await context.new_page()

        # Navigate to task URL
        await send_progress(f"Navigating to task: {task_url}")
        await page.goto(task_url, wait_until='networkidle')

        # Wait for page to load
        await send_progress("Waiting for page to load...")
        await page.wait_for_load_state('networkidle')

        # Find chat input
        await send_progress("Looking for chat input...")
        chat_input_selector = sel.CHAT_INPUT_TEXTAREA_CSS

        try:
            await page.wait_for_selector(chat_input_selector, timeout=10000)
        except:
            await send_progress("❌ Chat input not found - checking if logged in...")
            # Check if we need to login
            current_url = page.url
            if "login" in current_url or "auth" in current_url:
                await send_progress("❌ Not logged in. Please setup Chrome profile first.")
                await context.close()
                await playwright.stop()
                return {
                    "success": False,
                    "error": "Not logged in. Please setup Chrome profile first.",
                    "current_url": current_url
                }
            else:
                await send_progress("❌ Chat interface not available on this page")
                await context.close()
                await playwright.stop()
                return {
                    "success": False,
                    "error": "Chat interface not found on this page",
                    "current_url": current_url
                }

        # Get current messages count for comparison
        await send_progress("Counting existing messages...")
        existing_messages = await page.query_selector_all(sel.CHAT_EVENT_CONTAINER_CSS)
        initial_message_count = len(existing_messages)

        # Type and send message
        await send_progress(f"Sending message: '{message[:50]}{'...' if len(message) > 50 else ''}'")
        await page.fill(chat_input_selector, message)
        await page.press(chat_input_selector, 'Enter')

        # Wait for new message to appear
        await send_progress("Waiting for Manus response...")
        max_wait_time = 30  # 30 seconds timeout
        wait_interval = 1   # Check every 1 second
        waited_time = 0

        new_response = None

        while waited_time < max_wait_time:
            await asyncio.sleep(wait_interval)
            waited_time += wait_interval

            # Check for new messages
            current_messages = await page.query_selector_all(sel.CHAT_EVENT_CONTAINER_CSS)
            current_count = len(current_messages)

            if current_count > initial_message_count:
                await send_progress(f"New message detected! ({current_count} vs {initial_message_count})")

                # Get the latest message
                latest_message = current_messages[-1]

                # Check if it's from Manus (not user)
                user_message_elements = await latest_message.query_selector_all(sel.USER_MESSAGE_TEXT_CSS)

                if not user_message_elements:  # No user message elements = Manus response
                    # Extract Manus response
                    manus_content_elements = await latest_message.query_selector_all(sel.MANUS_MESSAGE_CONTENT_PROSE_CSS)

                    if manus_content_elements:
                        manus_response = await manus_content_elements[0].inner_text()
                        await send_progress(f"✅ Got Manus response: '{manus_response[:100]}{'...' if len(manus_response) > 100 else ''}'")

                        # Check for attachments
                        attachments = []
                        attachment_elements = await latest_message.query_selector_all(sel.ATTACHMENT_IN_MESSAGE_CONTAINER_CSS)

                        for attachment in attachment_elements:
                            try:
                                filename_elem = await attachment.query_selector(sel.ATTACHMENT_IN_MESSAGE_FILENAME_CSS)
                                details_elem = await attachment.query_selector(sel.ATTACHMENT_IN_MESSAGE_DETAILS_CSS)

                                attachment_data = {}
                                if filename_elem:
                                    attachment_data["filename"] = await filename_elem.inner_text()
                                if details_elem:
                                    attachment_data["details"] = await details_elem.inner_text()

                                if attachment_data:
                                    attachments.append(attachment_data)
                            except:
                                pass

                        new_response = {
                            "user_message": message,
                            "manus_response": manus_response,
                            "timestamp": await get_current_timestamp(),
                            "attachments": attachments
                        }
                        break

            await send_progress(f"Still waiting... ({waited_time}/{max_wait_time}s)")

        if not new_response:
            await send_progress("⏰ Timeout waiting for Manus response")
            await context.close()
            await playwright.stop()
            return {
                "success": False,
                "error": "Timeout waiting for Manus response",
                "timeout": max_wait_time
            }

        # Crawl updated page data
        await send_progress("Crawling updated page data...")
        updated_data = await crawl_page_data_internal(page)

        await context.close()
        await playwright.stop()

        await send_progress("✅ Interactive chat completed successfully!")

        return {
            "success": True,
            "chat_response": new_response,
            "updated_page_data": updated_data,
            "timestamp": await get_current_timestamp()
        }

    except Exception as e:
        await send_progress(f"❌ Error: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "message": f"Error in interactive chat: {str(e)}"
        }

async def get_current_timestamp() -> str:
    """Get current timestamp in ISO format."""
    from datetime import datetime
    return datetime.now().isoformat()

async def crawl_page_data_internal(page) -> Dict[str, Any]:
    """Internal function to crawl page data from an existing page."""
    data = {
        "page_title": "",
        "tasks": [],
        "current_task_title": "",
        "chat_messages": [],
        "footer_user": {}
    }

    # Get page title
    try:
        data["page_title"] = await page.title()
    except:
        pass

    # Get tasks from sidebar
    try:
        task_containers = await page.query_selector_all(sel.TASK_ITEM_CONTAINER_CSS)
        for container in task_containers:
            task_data = {}

            # Icon
            try:
                icon_img = await container.query_selector(sel.TASK_ITEM_ICON_IMG_CSS)
                if icon_img:
                    task_data["icon_src"] = await icon_img.get_attribute("src")
            except:
                pass

            # Title
            try:
                title_span = await container.query_selector(sel.TASK_ITEM_TITLE_SPAN_CSS)
                if title_span:
                    task_data["title"] = await title_span.get_attribute("title")
                    task_data["title_text"] = await title_span.inner_text()
            except:
                pass

            # Timestamp
            try:
                timestamp_span = await container.query_selector(sel.TASK_ITEM_TIMESTAMP_CSS)
                if timestamp_span:
                    task_data["timestamp"] = await timestamp_span.inner_text()
            except:
                pass

            # Preview
            try:
                preview_span = await container.query_selector(sel.TASK_ITEM_PREVIEW_SPAN_CSS)
                if preview_span:
                    task_data["preview"] = await preview_span.get_attribute("title")
            except:
                pass

            if task_data:
                data["tasks"].append(task_data)
    except:
        pass

    # Get current task title
    try:
        current_title_elem = await page.query_selector(sel.CURRENT_TASK_TITLE_MAIN_CSS)
        if current_title_elem:
            data["current_task_title"] = await current_title_elem.inner_text()
    except:
        pass

    # Get chat messages
    try:
        chat_events = await page.query_selector_all(sel.CHAT_EVENT_CONTAINER_CSS)
        for event in chat_events:
            message_data = {
                "event_id": await event.get_attribute("data-event-id")
            }

            # User message
            try:
                user_msg = await event.query_selector(sel.USER_MESSAGE_TEXT_CSS)
                if user_msg:
                    message_data["user_message"] = await user_msg.inner_text()
                    message_data["type"] = "user"
            except:
                pass

            # Manus message
            try:
                manus_msg = await event.query_selector(sel.MANUS_MESSAGE_CONTENT_PROSE_CSS)
                if manus_msg:
                    manus_text = await manus_msg.inner_text()
                    manus_html = await manus_msg.inner_html()

                    message_data["manus_message"] = manus_text
                    message_data["manus_html"] = manus_html
                    message_data["type"] = "manus"

                    # Message classification
                    try:
                        classification = ManusMessageClassifier.classify_message_type(
                            manus_html=manus_html,
                            manus_text=manus_text
                        )
                        message_data["message_subtype"] = classification.get("message_subtype", "text")
                    except:
                        message_data["message_subtype"] = "text"
            except:
                pass

            # Timestamp
            try:
                timestamp_elem = await event.query_selector(sel.MESSAGE_TIMESTAMP_CSS)
                if timestamp_elem:
                    message_data["timestamp"] = await timestamp_elem.inner_text()
            except:
                pass

            if "user_message" in message_data or "manus_message" in message_data:
                data["chat_messages"].append(message_data)
    except:
        pass

    # Get footer user info
    try:
        footer_avatar = await page.query_selector(sel.FOOTER_USER_AVATAR_IMG_CSS)
        footer_name = await page.query_selector(sel.FOOTER_USER_NAME_SPAN_CSS)

        if footer_avatar or footer_name:
            data["footer_user"] = {
                "avatar_src": await footer_avatar.get_attribute("src") if footer_avatar else None,
                "name": await footer_name.inner_text() if footer_name else None
            }
    except:
        pass

    return data
