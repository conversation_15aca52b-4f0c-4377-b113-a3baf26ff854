<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manus Crawler Realtime</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        #container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }

        h2 {
            color: #555;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }

        input[type="text"], input[type="url"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        input[type="checkbox"] {
            margin-right: 8px;
        }

        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }

        button:hover {
            background-color: #0056b3;
        }

        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }

        #statusArea {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            min-height: 100px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
        }

        #resultArea {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            min-height: 200px;
            max-height: 400px;
            overflow: auto;
            font-family: monospace;
            font-size: 12px;
        }

        .status-connected {
            color: #28a745;
            font-weight: bold;
        }

        .status-disconnected {
            color: #dc3545;
            font-weight: bold;
        }

        .status-connecting {
            color: #ffc107;
            font-weight: bold;
        }

        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div id="container">
        <h1>🕷️ Manus Crawler - Realtime Status</h1>

        <div class="form-group">
            <label for="urlInput">URL để Crawl:</label>
            <input type="url" id="urlInput" value="https://manus.im/" size="50">
        </div>

        <div class="form-group">
            <label for="profileNameInput">Tên Profile (tùy chọn):</label>
            <input type="text" id="profileNameInput" placeholder="my_manus_profile">
        </div>

        <div class="form-group">
            <input type="checkbox" id="headlessCheckbox" checked>
            <label for="headlessCheckbox" style="display: inline;">Chạy Headless</label>
        </div>

        <div class="form-group">
            <label for="requestIdInput">Request ID (tự động tạo):</label>
            <input type="text" id="requestIdInput" readonly size="36">
        </div>

        <div class="form-group">
            <span>WebSocket Status: </span>
            <span id="wsStatus" class="status-disconnected">Disconnected</span>
        </div>

        <button onclick="startCrawl()" id="crawlBtn">🚀 Bắt đầu Crawl Realtime</button>
        <button onclick="clearResults()" id="clearBtn">🗑️ Xóa Kết quả</button>

        <div class="grid">
            <div>
                <h2>📊 Trạng thái Crawl:</h2>
                <div id="statusArea">Chưa có hoạt động...</div>
            </div>

            <div>
                <h2>📋 Kết quả (JSON):</h2>
                <pre id="resultArea">Chưa có dữ liệu...</pre>
            </div>
        </div>
    </div>

    <script>
        let socket = null;
        let currentRequestId = null;

        // Tạo UUID v4
        function generateUUID() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                const r = Math.random() * 16 | 0;
                const v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        }

        // Cập nhật trạng thái WebSocket
        function updateWSStatus(status) {
            const wsStatusElement = document.getElementById('wsStatus');
            wsStatusElement.className = `status-${status}`;

            switch(status) {
                case 'connected':
                    wsStatusElement.textContent = 'Connected';
                    break;
                case 'connecting':
                    wsStatusElement.textContent = 'Connecting...';
                    break;
                case 'disconnected':
                    wsStatusElement.textContent = 'Disconnected';
                    break;
            }
        }

        // Kết nối WebSocket
        function connectWebSocket(requestId) {
            if (socket) {
                socket.close();
            }

            updateWSStatus('connecting');

            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws/crawl-status/${requestId}`;

            console.log('🔗 Connecting to WebSocket:', wsUrl);
            logStatus(`🔗 Connecting to WebSocket: ${wsUrl}`);

            socket = new WebSocket(wsUrl);

            socket.onopen = function(event) {
                updateWSStatus('connected');
                logStatus(`✅ WebSocket connected for request: ${requestId}`);
            };

            socket.onmessage = function(event) {
                try {
                    const message = JSON.parse(event.data);

                    if (message.type === 'progress') {
                        logStatus(`📝 ${message.message}`);
                    } else if (message.type === 'data') {
                        logStatus(`✅ Crawl completed successfully!`);
                        displayResult(message.data);
                        enableCrawlButton();
                    } else if (message.type === 'error') {
                        logStatus(`❌ Error: ${message.message}`);
                        enableCrawlButton();
                    }
                } catch (e) {
                    logStatus(`❌ Error parsing WebSocket message: ${e.message}`);
                }
            };

            socket.onclose = function(event) {
                updateWSStatus('disconnected');
                logStatus(`🔌 WebSocket disconnected`);
                enableCrawlButton();
            };

            socket.onerror = function(error) {
                updateWSStatus('disconnected');
                console.error('❌ WebSocket error:', error);
                logStatus(`❌ WebSocket error: ${error.type || 'Connection failed'}`);
                logStatus(`💡 Make sure server is running on http://localhost:8000`);
                enableCrawlButton();
            };
        }

        // Log status message
        function logStatus(message) {
            const statusArea = document.getElementById('statusArea');
            const timestamp = new Date().toLocaleTimeString();
            statusArea.textContent += `[${timestamp}] ${message}\n`;
            statusArea.scrollTop = statusArea.scrollHeight;
        }

        // Hiển thị kết quả
        function displayResult(data) {
            const resultArea = document.getElementById('resultArea');
            resultArea.textContent = JSON.stringify(data, null, 2);
        }

        // Enable/disable crawl button
        function disableCrawlButton() {
            const btn = document.getElementById('crawlBtn');
            btn.disabled = true;
            btn.textContent = '⏳ Đang Crawl...';
        }

        function enableCrawlButton() {
            const btn = document.getElementById('crawlBtn');
            btn.disabled = false;
            btn.textContent = '🚀 Bắt đầu Crawl Realtime';
        }

        // Bắt đầu crawl
        async function startCrawl() {
            const url = document.getElementById('urlInput').value.trim();
            const profileName = document.getElementById('profileNameInput').value.trim();
            const headless = document.getElementById('headlessCheckbox').checked;

            if (!url) {
                alert('Vui lòng nhập URL!');
                return;
            }

            // Tạo request ID mới
            currentRequestId = generateUUID();
            document.getElementById('requestIdInput').value = currentRequestId;

            // Kết nối WebSocket
            connectWebSocket(currentRequestId);

            // Disable button
            disableCrawlButton();

            // Clear previous results
            document.getElementById('statusArea').textContent = '';
            document.getElementById('resultArea').textContent = 'Đang chờ dữ liệu...';

            logStatus(`🚀 Starting crawl for URL: ${url}`);

            // Gửi request HTTP
            try {
                const requestBody = {
                    url: url,
                    request_id: currentRequestId,
                    headless: headless
                };

                if (profileName) {
                    requestBody.profile_name = profileName;
                }

                const response = await fetch('/crawl-url-realtime/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody)
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                logStatus(`📡 Request sent: ${result.message}`);

            } catch (error) {
                logStatus(`❌ Error sending request: ${error.message}`);
                enableCrawlButton();
            }
        }

        // Xóa kết quả
        function clearResults() {
            document.getElementById('statusArea').textContent = 'Chưa có hoạt động...';
            document.getElementById('resultArea').textContent = 'Chưa có dữ liệu...';
            document.getElementById('requestIdInput').value = '';

            if (socket) {
                socket.close();
            }
        }

        // Khởi tạo khi trang load
        window.onload = function() {
            updateWSStatus('disconnected');
        };
    </script>
</body>
</html>
