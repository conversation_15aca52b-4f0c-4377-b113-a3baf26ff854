"""
Pydantic models for request/response schemas
"""

from typing import Optional, Dict, Any, List
from pydantic import BaseModel

# Task and Chat Models
class TaskItem(BaseModel):
    icon_src: Optional[str] = None
    title: Optional[str] = None
    title_text: Optional[str] = None
    timestamp: Optional[str] = None
    preview: Optional[str] = None

class ChatMessage(BaseModel):
    event_id: Optional[str] = None
    type: Optional[str] = None
    user_message: Optional[str] = None
    manus_message: Optional[str] = None
    manus_html: Optional[str] = None
    timestamp: Optional[str] = None
    # Thêm các field mới cho phân loại message
    message_subtype: Optional[str] = None  # "text", "code", "file", "list", "mixed"
    content_analysis: Optional[Dict[str, Any]] = None  # Chi tiết phân tích nội dung

class FooterUserInfo(BaseModel):
    avatar_src: Optional[str] = None
    name: Optional[str] = None

class ProfileStatus(BaseModel):
    profile_name: Optional[str] = None
    use_system_profile: bool = False
    headless: bool = True

class CrawledDataResponse(BaseModel):
    page_title: str = ""
    tasks: List[TaskItem] = []
    current_task_title: str = ""
    chat_messages: List[ChatMessage] = []
    footer_user: FooterUserInfo = FooterUserInfo()
    profile_status: ProfileStatus = ProfileStatus()

# Request Models
class CrawlUrlRequest(BaseModel):
    url: str
    profile_name: Optional[str] = None
    use_system_profile: bool = False
    headless: bool = True

class CrawlUrlRequestWithId(CrawlUrlRequest):
    request_id: str

class CrawlHtmlRequest(BaseModel):
    html_content: str
    profile_name: Optional[str] = None
    use_system_profile: bool = False
    headless: bool = True

class CrawlHtmlRequestWithId(CrawlHtmlRequest):
    request_id: str

class SetupProfileRequest(BaseModel):
    profile_name: str
    url: str = "https://manus.im/"

# WebSocket Models
class WebSocketMessage(BaseModel):
    type: str  # "progress", "data", "error"
    message: Optional[str] = None
    data: Optional[Dict[str, Any]] = None

# Response Models
class StandardResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None

class HealthResponse(BaseModel):
    status: str
    message: str

class ProfileListResponse(BaseModel):
    success: bool
    profiles: List[Dict[str, Any]] = []
    total: int = 0
    base_path: Optional[str] = None
    error: Optional[str] = None

# Interactive Chat Models
class ChatWithManusRequest(BaseModel):
    message: str
    task_url: str
    profile_name: Optional[str] = None
    request_id: str
    headless: bool = True

class ChatResponse(BaseModel):
    user_message: str
    manus_response: str
    timestamp: str
    attachments: List[Dict[str, Any]] = []

class InteractiveChatResult(BaseModel):
    success: bool
    chat_response: Optional[ChatResponse] = None
    updated_page_data: Optional[CrawledDataResponse] = None
    timestamp: str
    error: Optional[str] = None
