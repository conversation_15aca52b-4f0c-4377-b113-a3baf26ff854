# 🚀 Frontend NextJS - Tích hợp với Manus Crawler Backend

**✅ YÊU CẦU** - Tài liệu này mô tả việc xây dựng Frontend NextJS hiện đại để tích hợp với hệ thống Manus Crawler Backend FastAPI đã hoàn thành.

## 🎯 **Mục tiêu Frontend:**
- ✅ **NextJS 14+ App Router** với TypeScript
- ✅ **Tích hợp FastAPI Backend** qua REST API và WebSocket
- ✅ **UI/UX giống Manus.im** với TailwindCSS (dark theme, sidebar layout)
- ✅ **Realtime Updates** với WebSocket integration
- ✅ **Admin Dashboard** để quản lý Chrome profiles
- ✅ **Responsive Design** cho desktop và mobile
- ✅ **Type Safety** với TypeScript và Zod validation
- ✅ **Manus-style Layout** với sidebar tasks và main chat area

## 📋 Mục Lục

1. [🏗️ Cấu trúc dự án NextJS](#1-cấu-trúc-dự-án-nextjs)
2. [⚙️ Setup và Configuration](#2-setup-và-configuration)
3. [🔌 API Integration](#3-api-integration)
4. [🎨 UI Components](#4-ui-components)
5. [📡 WebSocket Realtime](#5-websocket-realtime)
6. [🔐 Admin Dashboard](#6-admin-dashboard)
7. [🧪 Testing & Quality](#7-testing--quality)

---

## 1. 🏗️ Cấu trúc dự án NextJS

### Tổng quan cấu trúc

```
frontend/
├── 📁 app/                          # NextJS 14 App Router
│   ├── layout.tsx                   # Root layout
│   ├── page.tsx                     # Home page
│   ├── globals.css                  # Global styles
│   ├── 📁 crawler/                  # Crawler interface
│   │   ├── page.tsx                 # Main crawler UI
│   │   └── layout.tsx               # Crawler layout
│   ├── 📁 admin/                    # Admin dashboard
│   │   ├── page.tsx                 # Admin main page
│   │   ├── profiles/                # Profile management
│   │   └── layout.tsx               # Admin layout
│   └── 📁 api/                      # API routes (proxy)
│       └── proxy/                   # Backend proxy routes
├── 📁 components/                   # Reusable components
│   ├── 📁 ui/                       # shadcn/ui components
│   ├── 📁 crawler/                  # Crawler-specific components
│   ├── 📁 admin/                    # Admin-specific components
│   └── 📁 common/                   # Common components
├── 📁 lib/                          # Utilities and configurations
│   ├── api.ts                       # API client
│   ├── websocket.ts                 # WebSocket manager
│   ├── types.ts                     # TypeScript types
│   └── utils.ts                     # Utility functions
├── 📁 hooks/                        # Custom React hooks
│   ├── use-crawler.ts               # Crawler state management
│   ├── use-websocket.ts             # WebSocket hook
│   └── use-admin.ts                 # Admin operations
├── 📁 stores/                       # State management
│   ├── crawler-store.ts             # Crawler state (Zustand)
│   └── admin-store.ts               # Admin state
├── package.json                     # Dependencies
├── tailwind.config.js               # Tailwind configuration
├── next.config.js                   # NextJS configuration
└── tsconfig.json                    # TypeScript configuration
```

### Core Dependencies

```json
{
  "dependencies": {
    "next": "^14.0.0",
    "react": "^18.0.0",
    "react-dom": "^18.0.0",
    "@types/node": "^20.0.0",
    "@types/react": "^18.0.0",
    "@types/react-dom": "^18.0.0",
    "typescript": "^5.0.0",
    "tailwindcss": "^3.3.0",
    "@tailwindcss/forms": "^0.5.0",
    "@radix-ui/react-*": "latest",
    "class-variance-authority": "^0.7.0",
    "clsx": "^2.0.0",
    "tailwind-merge": "^2.0.0",
    "lucide-react": "^0.300.0",
    "zod": "^3.22.0",
    "zustand": "^4.4.0",
    "axios": "^1.6.0",
    "react-hook-form": "^7.48.0",
    "@hookform/resolvers": "^3.3.0"
  },
  "devDependencies": {
    "eslint": "^8.0.0",
    "eslint-config-next": "^14.0.0",
    "@types/ws": "^8.5.0",
    "prettier": "^3.0.0"
  }
}
```

---

## 2. ⚙️ Setup và Configuration

### NextJS Configuration

**`next.config.js`**
```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  env: {
    BACKEND_URL: process.env.BACKEND_URL || 'http://localhost:8000',
    WS_URL: process.env.WS_URL || 'ws://localhost:8000',
  },
  async rewrites() {
    return [
      {
        source: '/api/backend/:path*',
        destination: `${process.env.BACKEND_URL || 'http://localhost:8000'}/:path*`,
      },
    ];
  },
};

module.exports = nextConfig;
```

### Environment Variables

**`.env.local`**
```env
BACKEND_URL=http://localhost:8000
WS_URL=ws://localhost:8000
ADMIN_API_KEY=your_super_secret_key_here
```

### Tailwind Configuration (Manus-style)

**`tailwind.config.js`**
```javascript
/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ["class"],
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
  ],
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        // Manus-style CSS Variables
        'background-nav': 'var(--background-nav)',
        'background-menu-white': 'var(--background-menu-white)',
        'fill-tsp-gray-main': 'var(--fill-tsp-gray-main)',
        'fill-tsp-white-main': 'var(--fill-tsp-white-main)',
        'fill-tsp-white-dark': 'var(--fill-tsp-white-dark)',
        'fill-tsp-white-light': 'var(--fill-tsp-white-light)',
        'text-primary': 'var(--text-primary)',
        'text-secondary': 'var(--text-secondary)',
        'text-tertiary': 'var(--text-tertiary)',
        'icon-primary': 'var(--icon-primary)',
        'icon-secondary': 'var(--icon-secondary)',
        'border-main': 'var(--border-main)',
        'border-light': 'var(--border-light)',
        'shadow-S': 'var(--shadow-S)',
        'Button-primary-white': 'var(--Button-primary-white)',

        // Standard shadcn colors
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: 0 },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: 0 },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [require("tailwindcss-animate"), require("@tailwindcss/forms")],
}
```

### Global CSS với Manus Variables

**`app/globals.css`**
```css
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Manus-style CSS Variables */
:root {
  /* Dark theme colors matching Manus.im */
  --background-nav: #1a1a1a;
  --background-menu-white: #2a2a2a;
  --fill-tsp-gray-main: rgba(255, 255, 255, 0.1);
  --fill-tsp-white-main: #3a3a3a;
  --fill-tsp-white-dark: #2a2a2a;
  --fill-tsp-white-light: #4a4a4a;
  --text-primary: #ffffff;
  --text-secondary: #cccccc;
  --text-tertiary: #888888;
  --icon-primary: #ffffff;
  --icon-secondary: #cccccc;
  --border-main: rgba(255, 255, 255, 0.2);
  --border-light: rgba(255, 255, 255, 0.1);
  --shadow-S: 0 1px 3px rgba(0, 0, 0, 0.3);
  --Button-primary-white: #ffffff;

  /* Standard shadcn variables */
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --primary: 210 40% 98%;
  --primary-foreground: 222.2 84% 4.9%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 212.7 26.8% 83.9%;
  --radius: 0.5rem;
}

/* Base styles */
* {
  border-color: hsl(var(--border));
}

body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

/* Manus-style scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--fill-tsp-gray-main);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}

/* Manus-style transitions */
.manus-transition {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Manus-style hover effects */
.manus-hover:hover {
  background-color: var(--fill-tsp-gray-main);
}
```

---

## 3. 🔌 API Integration

### TypeScript Types

**`lib/types.ts`**
```typescript
import { z } from 'zod';

// Backend API Types
export const TaskItemSchema = z.object({
  icon_src: z.string().optional(),
  title: z.string().optional(),
  timestamp: z.string().optional(),
});

export const ChatMessageSchema = z.object({
  sender: z.enum(['user', 'manus']),
  content: z.string(),
  timestamp: z.string().optional(),
  attachments: z.array(z.object({
    filename: z.string(),
    details: z.string().optional(),
  })).optional(),
});

export const CrawledDataSchema = z.object({
  page_title: z.string().default(''),
  tasks: z.array(TaskItemSchema).default([]),
  chat_messages: z.array(ChatMessageSchema).default([]),
  profile_status: z.object({
    profile_name: z.string().optional(),
    profile_exists: z.boolean(),
    profile_path: z.string().optional(),
  }).optional(),
});

export const CrawlRequestSchema = z.object({
  url: z.string().url(),
  profile_name: z.string().optional(),
  headless: z.boolean().default(true),
  request_id: z.string().uuid(),
});

export const SetupProfileRequestSchema = z.object({
  profile_name: z.string().min(1),
  url: z.string().url().default('https://manus.im/'),
});

// Inferred Types
export type TaskItem = z.infer<typeof TaskItemSchema>;
export type ChatMessage = z.infer<typeof ChatMessageSchema>;
export type CrawledData = z.infer<typeof CrawledDataSchema>;
export type CrawlRequest = z.infer<typeof CrawlRequestSchema>;
export type SetupProfileRequest = z.infer<typeof SetupProfileRequestSchema>;

// WebSocket Message Types
export type WebSocketMessage =
  | { type: 'progress'; message: string }
  | { type: 'data'; data: CrawledData }
  | { type: 'error'; message: string };
```

### API Client

**`lib/api.ts`**
```typescript
import axios from 'axios';
import { CrawlRequest, CrawledData, SetupProfileRequest } from './types';

const API_BASE_URL = process.env.BACKEND_URL || 'http://localhost:8000';

export const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
});

// API Functions
export const crawlApi = {
  // Sync crawl
  crawlUrl: async (request: Omit<CrawlRequest, 'request_id'>): Promise<CrawledData> => {
    const response = await api.post('/crawl-url/', request);
    return response.data;
  },

  // Realtime crawl (returns immediately, data comes via WebSocket)
  crawlUrlRealtime: async (request: CrawlRequest): Promise<{ status: string }> => {
    const response = await api.post('/crawl-url-realtime/', request);
    return response.data;
  },

  // Interactive chat with Manus (NEW)
  chatWithManusRealtime: async (request: ChatWithManusRequest): Promise<{ status: string; request_id: string }> => {
    const response = await api.post('/chat-with-manus-realtime/', request);
    return response.data;
  },

  // Health check
  healthCheck: async (): Promise<{ status: string }> => {
    const response = await api.get('/health');
    return response.data;
  },
};

export const adminApi = {
  // Setup Chrome profile
  setupProfile: async (
    request: SetupProfileRequest,
    apiKey: string
  ): Promise<{ status: string; message: string }> => {
    const response = await api.post('/admin/setup-chrome-profile/', request, {
      headers: { 'X-API-KEY': apiKey },
    });
    return response.data;
  },

  // List profiles
  listProfiles: async (apiKey: string): Promise<string[]> => {
    const response = await api.get('/admin/list-profiles/', {
      headers: { 'X-API-KEY': apiKey },
    });
    return response.data;
  },

  // Delete profile
  deleteProfile: async (profileName: string, apiKey: string): Promise<{ status: string }> => {
    const response = await api.delete(`/admin/delete-profile/${profileName}`, {
      headers: { 'X-API-KEY': apiKey },
    });
    return response.data;
  },
};
```

---

## 4. 🎨 Manus-style UI Components

### Main Layout (Manus-style)

**`app/layout.tsx`**
```typescript
import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { Toaster } from '@/components/ui/toaster';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Manus Crawler - NextJS Frontend',
  description: 'Modern frontend for Manus.im crawler with realtime updates',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className="dark">
      <body className={inter.className}>
        <div className="flex w-full h-screen overflow-hidden bg-background">
          {children}
        </div>
        <Toaster />
      </body>
    </html>
  );
}
```

### Manus-style Sidebar Component

**`components/manus/sidebar.tsx`**
```typescript
'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { TaskItem } from '@/lib/types';
import { PanelRight, Search, Plus, Command, MoreHorizontal } from 'lucide-react';

interface SidebarProps {
  tasks: TaskItem[];
  onTaskSelect?: (task: TaskItem) => void;
  onNewTask?: () => void;
}

export function ManusStyleSidebar({ tasks, onTaskSelect, onNewTask }: SidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false);

  return (
    <div
      className="h-full flex flex-col bg-background-nav manus-transition"
      style={{
        width: isCollapsed ? '0px' : '300px',
        transition: 'width 0.28s cubic-bezier(0.4, 0, 0.2, 1)'
      }}
    >
      {/* Header */}
      <div className="flex">
        <div className="flex items-center px-3 py-3 flex-row h-[52px] gap-1 justify-end w-full">
          <div className="flex justify-between w-full px-1 pt-2">
            <div className="relative flex">
              <button
                onClick={() => setIsCollapsed(!isCollapsed)}
                className="flex h-7 w-7 items-center justify-center cursor-pointer hover:bg-fill-tsp-gray-main rounded-md manus-transition"
              >
                <PanelRight className="h-5 w-5 text-icon-secondary" />
              </button>
            </div>
            <div className="flex flex-row gap-1">
              <button className="flex h-7 w-7 items-center justify-center cursor-pointer hover:bg-fill-tsp-gray-main rounded-md manus-transition">
                <Search className="h-5 w-5 text-icon-secondary" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* New Task Button */}
      <div className="px-3 mb-1 flex justify-center flex-shrink-0">
        <button
          onClick={onNewTask}
          className="flex min-w-[36px] w-full items-center justify-center gap-1.5 rounded-lg h-[32px] bg-Button-primary-white hover:bg-white/20 dark:hover:bg-black/60 cursor-pointer shadow-[0px_0.5px_3px_0px_var(--shadow-S)] manus-transition"
        >
          <Plus className="h-4 w-4 text-icon-primary" />
          <span className="text-sm font-medium text-text-primary whitespace-nowrap truncate">
            New task
          </span>
          <div className="flex items-center gap-0.5">
            <span className="flex text-text-tertiary justify-center items-center min-w-5 h-5 px-1 rounded-[4px] bg-fill-tsp-white-light border border-border-light">
              <Command className="h-3 w-3" />
            </span>
            <span className="flex justify-center items-center w-5 h-5 px-1 rounded-[4px] bg-fill-tsp-white-light border border-border-light text-sm font-normal text-text-tertiary">
              K
            </span>
          </div>
        </button>
      </div>

      {/* Tasks List */}
      <ScrollArea className="flex-1 px-2 pb-5 overflow-x-hidden">
        <div className="space-y-1">
          {tasks.map((task, index) => (
            <TaskItemComponent
              key={index}
              task={task}
              onClick={() => onTaskSelect?.(task)}
            />
          ))}
        </div>
      </ScrollArea>
    </div>
  );
}

interface TaskItemComponentProps {
  task: TaskItem;
  onClick?: () => void;
}

function TaskItemComponent({ task, onClick }: TaskItemComponentProps) {
  return (
    <div
      onClick={onClick}
      className="group flex h-14 cursor-pointer items-center gap-2 rounded-[10px] px-2 manus-transition hover:bg-fill-tsp-gray-main"
    >
      {/* Task Icon */}
      <div className="relative">
        <div className="h-8 w-8 rounded-full flex items-center justify-center relative bg-fill-tsp-white-dark">
          <div className="relative h-4 w-4 object-cover brightness-0 opacity-75 dark:opacity-100 dark:brightness-100">
            {task.icon_src ? (
              <img
                alt={task.title || 'Task'}
                className="w-full h-full object-cover"
                src={task.icon_src}
              />
            ) : (
              <div className="w-full h-full bg-fill-tsp-white-main rounded-full" />
            )}
          </div>
        </div>
      </div>

      {/* Task Content */}
      <div className="min-w-20 flex-1 manus-transition opacity-100">
        <div className="flex items-center gap-1 overflow-x-hidden">
          <span
            className="truncate text-sm font-medium text-text-primary flex-1 min-w-0"
            title={task.title}
          >
            {task.title || 'Untitled Task'}
          </span>
          <span className="text-text-tertiary text-xs whitespace-nowrap">
            {task.timestamp}
          </span>
        </div>
        <div className="flex items-center gap-2 h-[18px] relative">
          <span
            className="min-w-0 flex-1 truncate text-xs text-text-tertiary"
            title={task.preview || task.title}
          >
            {task.preview || task.title || 'No preview available'}
          </span>
          <div className="w-[22px] h-[22px] flex rounded-[6px] items-center justify-center pointer invisible cursor-pointer bg-background-menu-white border border-border-main shadow-sm group-hover:visible touch-device:visible">
            <MoreHorizontal className="h-4 w-4 text-icon-secondary" />
          </div>
        </div>
      </div>
    </div>
  );
}
```

### Manus-style Main Content Area

**`components/manus/main-content.tsx`**
```typescript
'use client';

import { ScrollArea } from '@/components/ui/scroll-area';
import { ChatMessage } from '@/lib/types';
import { Send, Paperclip } from 'lucide-react';
import { useState } from 'react';

interface MainContentProps {
  title?: string;
  messages: ChatMessage[];
  onSendMessage?: (message: string) => void;
  isLoading?: boolean;
}

export function ManusStyleMainContent({
  title = "Manus Crawler",
  messages,
  onSendMessage,
  isLoading = false
}: MainContentProps) {
  const [inputValue, setInputValue] = useState('');

  const handleSend = () => {
    if (inputValue.trim() && onSendMessage) {
      onSendMessage(inputValue.trim());
      setInputValue('');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <div className="flex-1 flex flex-col h-full bg-background">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-background border-b border-border-main">
        <div className="px-6 py-4">
          <div className="text-text-primary text-lg font-medium">
            <span className="whitespace-nowrap text-ellipsis overflow-hidden">
              {title}
            </span>
          </div>
        </div>
      </div>

      {/* Messages Area */}
      <ScrollArea className="flex-1 px-6">
        <div className="space-y-4 py-4">
          {messages.length === 0 ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="text-text-tertiary text-lg mb-2">
                  No messages yet
                </div>
                <div className="text-text-tertiary text-sm">
                  Start a conversation with Manus Crawler
                </div>
              </div>
            </div>
          ) : (
            messages.map((message, index) => (
              <ChatMessageComponent key={index} message={message} />
            ))
          )}
          {isLoading && (
            <div className="flex items-center space-x-2 text-text-tertiary">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-text-tertiary"></div>
              <span>Manus is thinking...</span>
            </div>
          )}
        </div>
      </ScrollArea>

      {/* Input Area */}
      <div className="border-t border-border-main p-4">
        <div className="flex items-end space-x-2">
          <div className="flex-1 relative">
            <textarea
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Send message to Manus"
              className="w-full resize-none rounded-lg border border-border-main bg-background-menu-white px-4 py-3 text-text-primary placeholder-text-tertiary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent min-h-[44px] max-h-32"
              rows={1}
              disabled={isLoading}
            />
          </div>
          <button
            onClick={handleSend}
            disabled={!inputValue.trim() || isLoading}
            className="flex items-center justify-center w-10 h-10 rounded-lg bg-primary text-primary-foreground hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed manus-transition"
          >
            <Send className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  );
}

interface ChatMessageComponentProps {
  message: ChatMessage;
}

function ChatMessageComponent({ message }: ChatMessageComponentProps) {
  const isUser = message.sender === 'user';

  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'}`}>
      <div className={`max-w-[80%] ${isUser ? 'items-end' : 'items-start'} flex flex-col`}>
        {/* Message Content */}
        <div
          className={`rounded-lg px-4 py-3 ${
            isUser
              ? 'bg-primary text-primary-foreground rounded-br-none'
              : 'bg-fill-tsp-white-main text-text-primary rounded-bl-none'
          }`}
        >
          <div className="whitespace-pre-wrap break-words">
            {message.content}
          </div>

          {/* Attachments */}
          {message.attachments && message.attachments.length > 0 && (
            <div className="mt-2 space-y-2">
              {message.attachments.map((attachment, index) => (
                <div
                  key={index}
                  className="rounded-[10px] bg-fill-tsp-white-main border border-border-light p-3 group/attach"
                >
                  <div className="flex items-center space-x-2">
                    <Paperclip className="h-4 w-4 text-icon-secondary" />
                    <div className="flex-1 min-w-0">
                      <div className="text-sm text-text-primary font-medium truncate">
                        {attachment.filename}
                      </div>
                      {attachment.details && (
                        <div className="text-xs text-text-tertiary">
                          {attachment.details}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Timestamp */}
        {message.timestamp && (
          <div className={`text-xs text-text-tertiary mt-1 ${isUser ? 'text-right' : 'text-left'}`}>
            {message.timestamp}
          </div>
        )}
      </div>
    </div>
  );
}
```

### Manus-style Crawler Page Layout

**`app/crawler/page.tsx`**
```typescript
'use client';

import { useState, useEffect } from 'react';
import { ManusStyleSidebar } from '@/components/manus/sidebar';
import { ManusStyleMainContent } from '@/components/manus/main-content';
import { useCrawlerStore } from '@/stores/crawler-store';
import { TaskItem, ChatMessage } from '@/lib/types';

export default function CrawlerPage() {
  const { results, isLoading, startCrawl } = useCrawlerStore();
  const [selectedTask, setSelectedTask] = useState<TaskItem | null>(null);

  // Convert crawled data to display format
  const tasks: TaskItem[] = results?.tasks || [];
  const messages: ChatMessage[] = results?.chat_messages || [];

  const handleTaskSelect = (task: TaskItem) => {
    setSelectedTask(task);
  };

  const handleNewTask = () => {
    // Implement new task creation
    console.log('New task clicked');
  };

  const handleSendMessage = (message: string) => {
    // Implement message sending
    console.log('Send message:', message);
  };

  return (
    <div className="flex w-full h-screen overflow-hidden">
      {/* Sidebar */}
      <ManusStyleSidebar
        tasks={tasks}
        onTaskSelect={handleTaskSelect}
        onNewTask={handleNewTask}
      />

      {/* Main Content */}
      <ManusStyleMainContent
        title={selectedTask?.title || "Manus Crawler"}
        messages={messages}
        onSendMessage={handleSendMessage}
        isLoading={isLoading}
      />
    </div>
  );
}
```

### Updated Types for Manus-style

**`lib/types.ts` (Updated)**
```typescript
import { z } from 'zod';

// Enhanced TaskItem with preview field
export const TaskItemSchema = z.object({
  icon_src: z.string().optional(),
  title: z.string().optional(),
  timestamp: z.string().optional(),
  preview: z.string().optional(), // Added for sidebar preview
});

// Enhanced ChatMessage for Manus-style display
export const ChatMessageSchema = z.object({
  sender: z.enum(['user', 'manus']),
  content: z.string(),
  timestamp: z.string().optional(),
  attachments: z.array(z.object({
    filename: z.string(),
    details: z.string().optional(),
  })).optional(),
});

// Rest of the schemas remain the same...
export const CrawledDataSchema = z.object({
  page_title: z.string().default(''),
  tasks: z.array(TaskItemSchema).default([]),
  chat_messages: z.array(ChatMessageSchema).default([]),
  profile_status: z.object({
    profile_name: z.string().optional(),
    profile_exists: z.boolean(),
    profile_path: z.string().optional(),
  }).optional(),
});

export const CrawlRequestSchema = z.object({
  url: z.string().url(),
  profile_name: z.string().optional(),
  headless: z.boolean().default(true),
  request_id: z.string().uuid(),
});

export const SetupProfileRequestSchema = z.object({
  profile_name: z.string().min(1),
  url: z.string().url().default('https://manus.im/'),
});

// Inferred Types
export type TaskItem = z.infer<typeof TaskItemSchema>;
export type ChatMessage = z.infer<typeof ChatMessageSchema>;
export type CrawledData = z.infer<typeof CrawledDataSchema>;
export type CrawlRequest = z.infer<typeof CrawlRequestSchema>;
export type SetupProfileRequest = z.infer<typeof SetupProfileRequestSchema>;

// WebSocket Message Types
// Interactive Chat Types
export const ChatWithManusRequestSchema = z.object({
  message: z.string().min(1),
  task_url: z.string().url(),
  profile_name: z.string().optional(),
  request_id: z.string().uuid(),
  headless: z.boolean().default(true),
});

export const ChatResponseSchema = z.object({
  user_message: z.string(),
  manus_response: z.string(),
  timestamp: z.string(),
  attachments: z.array(z.object({
    filename: z.string(),
    details: z.string().optional(),
  })).default([]),
});

export const InteractiveChatResultSchema = z.object({
  success: z.boolean(),
  chat_response: ChatResponseSchema.optional(),
  updated_page_data: CrawledDataSchema.optional(),
  timestamp: z.string(),
  error: z.string().optional(),
});

export type ChatWithManusRequest = z.infer<typeof ChatWithManusRequestSchema>;
export type ChatResponse = z.infer<typeof ChatResponseSchema>;
export type InteractiveChatResult = z.infer<typeof InteractiveChatResultSchema>;

export type WebSocketMessage =
  | { type: 'progress'; message: string }
  | { type: 'data'; data: CrawledData | InteractiveChatResult }
  | { type: 'error'; message: string };
```

---

## 5. 📡 WebSocket Realtime
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Spider, Settings, Home } from 'lucide-react';

const navigation = [
  { name: 'Home', href: '/', icon: Home },
  { name: 'Crawler', href: '/crawler', icon: Spider },
  { name: 'Admin', href: '/admin', icon: Settings },
];

export function Navigation() {
  const pathname = usePathname();

  return (
    <nav className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/" className="flex items-center space-x-2">
              <Spider className="h-6 w-6" />
              <span className="font-bold">Manus Crawler</span>
            </Link>
          </div>

          <div className="flex items-center space-x-4">
            {navigation.map((item) => {
              const Icon = item.icon;
              return (
                <Button
                  key={item.name}
                  variant={pathname === item.href ? 'default' : 'ghost'}
                  asChild
                >
                  <Link href={item.href} className="flex items-center space-x-2">
                    <Icon className="h-4 w-4" />
                    <span>{item.name}</span>
                  </Link>
                </Button>
              );
            })}
          </div>
        </div>
      </div>
    </nav>
  );
}
```

---

## 5. 📡 WebSocket Realtime

### WebSocket Manager

**`lib/websocket.ts`**
```typescript
import { WebSocketMessage } from './types';

export class WebSocketManager {
  private ws: WebSocket | null = null;
  private url: string;
  private requestId: string;
  private onMessage: (message: WebSocketMessage) => void;
  private onError: (error: Event) => void;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  constructor(
    requestId: string,
    onMessage: (message: WebSocketMessage) => void,
    onError: (error: Event) => void = () => {}
  ) {
    this.requestId = requestId;
    this.onMessage = onMessage;
    this.onError = onError;
    this.url = `${process.env.WS_URL || 'ws://localhost:8000'}/ws/crawl-status/${requestId}`;
  }

  connect(): void {
    try {
      this.ws = new WebSocket(this.url);

      this.ws.onopen = () => {
        console.log(`WebSocket connected for request: ${this.requestId}`);
        this.reconnectAttempts = 0;
      };

      this.ws.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          this.onMessage(message);
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error);
        }
      };

      this.ws.onclose = () => {
        console.log('WebSocket connection closed');
        this.attemptReconnect();
      };

      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        this.onError(error);
      };
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      this.onError(error as Event);
    }
  }

  private attemptReconnect(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

      setTimeout(() => {
        this.connect();
      }, 1000 * this.reconnectAttempts);
    }
  }

  disconnect(): void {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }

  isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }
}
```

### WebSocket Hook

**`hooks/use-websocket.ts`**
```typescript
import { useEffect, useRef, useState } from 'react';
import { WebSocketManager } from '@/lib/websocket';
import { WebSocketMessage } from '@/lib/types';

export function useWebSocket(requestId: string | null) {
  const [isConnected, setIsConnected] = useState(false);
  const [lastMessage, setLastMessage] = useState<WebSocketMessage | null>(null);
  const [error, setError] = useState<string | null>(null);
  const wsManager = useRef<WebSocketManager | null>(null);

  useEffect(() => {
    if (!requestId) return;

    const handleMessage = (message: WebSocketMessage) => {
      setLastMessage(message);
    };

    const handleError = (error: Event) => {
      setError('WebSocket connection error');
      setIsConnected(false);
    };

    wsManager.current = new WebSocketManager(requestId, handleMessage, handleError);
    wsManager.current.connect();

    const checkConnection = setInterval(() => {
      if (wsManager.current) {
        setIsConnected(wsManager.current.isConnected());
      }
    }, 1000);

    return () => {
      clearInterval(checkConnection);
      if (wsManager.current) {
        wsManager.current.disconnect();
        wsManager.current = null;
      }
    };
  }, [requestId]);

  return {
    isConnected,
    lastMessage,
    error,
    clearError: () => setError(null),
  };
}
```

---

## 6. 🔐 Admin Dashboard

### Admin Store

**`stores/admin-store.ts`**
```typescript
import { create } from 'zustand';
import { adminApi } from '@/lib/api';
import { SetupProfileRequest } from '@/lib/types';

interface AdminState {
  apiKey: string;
  profiles: string[];
  isLoading: boolean;
  error: string | null;

  // Actions
  setApiKey: (key: string) => void;
  loadProfiles: () => Promise<void>;
  setupProfile: (request: SetupProfileRequest) => Promise<boolean>;
  deleteProfile: (profileName: string) => Promise<boolean>;
  clearError: () => void;
}

export const useAdminStore = create<AdminState>((set, get) => ({
  apiKey: '',
  profiles: [],
  isLoading: false,
  error: null,

  setApiKey: (key: string) => {
    set({ apiKey: key });
  },

  loadProfiles: async () => {
    const { apiKey } = get();
    if (!apiKey) {
      set({ error: 'API Key is required' });
      return;
    }

    set({ isLoading: true, error: null });
    try {
      const profiles = await adminApi.listProfiles(apiKey);
      set({ profiles, isLoading: false });
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to load profiles',
        isLoading: false
      });
    }
  },

  setupProfile: async (request: SetupProfileRequest) => {
    const { apiKey } = get();
    if (!apiKey) {
      set({ error: 'API Key is required' });
      return false;
    }

    set({ isLoading: true, error: null });
    try {
      await adminApi.setupProfile(request, apiKey);
      await get().loadProfiles(); // Reload profiles
      set({ isLoading: false });
      return true;
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to setup profile',
        isLoading: false
      });
      return false;
    }
  },

  deleteProfile: async (profileName: string) => {
    const { apiKey } = get();
    if (!apiKey) {
      set({ error: 'API Key is required' });
      return false;
    }

    set({ isLoading: true, error: null });
    try {
      await adminApi.deleteProfile(profileName, apiKey);
      await get().loadProfiles(); // Reload profiles
      set({ isLoading: false });
      return true;
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to delete profile',
        isLoading: false
      });
      return false;
    }
  },

  clearError: () => {
    set({ error: null });
  },
}));
```

---

## 7. 🧪 Testing & Quality

### Package Scripts

**`package.json` scripts section:**
```json
{
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "type-check": "tsc --noEmit",
    "test": "jest",
    "test:watch": "jest --watch"
  }
}
```

### Development Commands

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Start production server
npm start

# Type checking
npm run type-check

# Linting
npm run lint
```

---

## 🎉 **Kết luận**

Frontend NextJS đã được thiết kế để:

✅ **Tích hợp hoàn hảo** với Manus Crawler Backend FastAPI
✅ **UI/UX hiện đại** với Tailwind CSS và shadcn/ui
✅ **Type Safety** với TypeScript và Zod validation
✅ **Realtime Updates** qua WebSocket
✅ **Admin Dashboard** đầy đủ chức năng
✅ **Responsive Design** cho mọi thiết bị
✅ **Production Ready** với NextJS 14 App Router

**Ready for development!** 🚀

---

## 8. 📱 Crawler Interface Components

### Main Crawler Page

**`app/crawler/page.tsx`**
```typescript
'use client';

import { useState } from 'react';
import { CrawlerForm } from '@/components/crawler/crawler-form';
import { CrawlerResults } from '@/components/crawler/crawler-results';
import { CrawlerStatus } from '@/components/crawler/crawler-status';
import { useCrawlerStore } from '@/stores/crawler-store';

export default function CrawlerPage() {
  const { isLoading, results, status } = useCrawlerStore();

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold tracking-tight">Manus Crawler</h1>
        <p className="text-muted-foreground mt-2">
          Crawl Manus.im pages with realtime updates
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="space-y-6">
          <CrawlerForm />
          <CrawlerStatus />
        </div>

        <div>
          <CrawlerResults />
        </div>
      </div>
    </div>
  );
}
```

### Crawler Form Component

**`components/crawler/crawler-form.tsx`**
```typescript
'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { v4 as uuidv4 } from 'uuid';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { CrawlRequestSchema, type CrawlRequest } from '@/lib/types';
import { useCrawlerStore } from '@/stores/crawler-store';
import { useAdminStore } from '@/stores/admin-store';
import { Play, Loader2 } from 'lucide-react';

export function CrawlerForm() {
  const { startCrawl, isLoading } = useCrawlerStore();
  const { profiles } = useAdminStore();

  const form = useForm<CrawlRequest>({
    resolver: zodResolver(CrawlRequestSchema),
    defaultValues: {
      url: 'https://manus.im/',
      headless: true,
      request_id: uuidv4(),
    },
  });

  const onSubmit = async (data: CrawlRequest) => {
    await startCrawl(data);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Play className="h-5 w-5" />
          <span>Crawler Configuration</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="url">URL to Crawl</Label>
            <Input
              id="url"
              placeholder="https://manus.im/app/your-task-id"
              {...form.register('url')}
            />
            {form.formState.errors.url && (
              <p className="text-sm text-destructive">
                {form.formState.errors.url.message}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="profile">Chrome Profile</Label>
            <Select
              value={form.watch('profile_name') || ''}
              onValueChange={(value) => form.setValue('profile_name', value || undefined)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a profile (optional)" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">No profile</SelectItem>
                {profiles.map((profile) => (
                  <SelectItem key={profile} value={profile}>
                    {profile}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="headless"
              checked={form.watch('headless')}
              onCheckedChange={(checked) => form.setValue('headless', !!checked)}
            />
            <Label htmlFor="headless">Run in headless mode</Label>
          </div>

          <Button type="submit" disabled={isLoading} className="w-full">
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Crawling...
              </>
            ) : (
              <>
                <Play className="mr-2 h-4 w-4" />
                Start Crawl
              </>
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
```

### Crawler Status Component

**`components/crawler/crawler-status.tsx`**
```typescript
'use client';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useCrawlerStore } from '@/stores/crawler-store';
import { Activity, CheckCircle, XCircle, Clock } from 'lucide-react';

export function CrawlerStatus() {
  const { status, isLoading, error, progress } = useCrawlerStore();

  const getStatusIcon = () => {
    if (error) return <XCircle className="h-4 w-4 text-destructive" />;
    if (isLoading) return <Activity className="h-4 w-4 text-blue-500 animate-pulse" />;
    if (status) return <CheckCircle className="h-4 w-4 text-green-500" />;
    return <Clock className="h-4 w-4 text-muted-foreground" />;
  };

  const getStatusBadge = () => {
    if (error) return <Badge variant="destructive">Error</Badge>;
    if (isLoading) return <Badge variant="secondary">Running</Badge>;
    if (status) return <Badge variant="default">Completed</Badge>;
    return <Badge variant="outline">Ready</Badge>;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {getStatusIcon()}
            <span>Crawl Status</span>
          </div>
          {getStatusBadge()}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {isLoading && progress !== undefined && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Progress</span>
              <span>{Math.round(progress)}%</span>
            </div>
            <Progress value={progress} />
          </div>
        )}

        {status && (
          <div className="space-y-2">
            <h4 className="font-medium">Latest Status:</h4>
            <p className="text-sm text-muted-foreground">{status}</p>
          </div>
        )}

        {error && (
          <div className="space-y-2">
            <h4 className="font-medium text-destructive">Error:</h4>
            <p className="text-sm text-destructive">{error}</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
```

---

## 9. 🎯 Crawler Results Display

### Results Component

**`components/crawler/crawler-results.tsx`**
```typescript
'use client';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useCrawlerStore } from '@/stores/crawler-store';
import { FileText, MessageSquare, List, Download } from 'lucide-react';
import { Button } from '@/components/ui/button';

export function CrawlerResults() {
  const { results } = useCrawlerStore();

  if (!results) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>Crawl Results</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground text-center py-8">
            No results yet. Start a crawl to see data here.
          </p>
        </CardContent>
      </Card>
    );
  }

  const downloadResults = () => {
    const dataStr = JSON.stringify(results, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `manus-crawl-${new Date().toISOString()}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>Crawl Results</span>
          </div>
          <Button variant="outline" size="sm" onClick={downloadResults}>
            <Download className="h-4 w-4 mr-2" />
            Download JSON
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="tasks">Tasks</TabsTrigger>
            <TabsTrigger value="messages">Messages</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <h4 className="font-medium">Page Title</h4>
                <p className="text-sm text-muted-foreground">
                  {results.page_title || 'No title'}
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium">Statistics</h4>
                <div className="flex space-x-2">
                  <Badge variant="secondary">
                    {results.tasks.length} Tasks
                  </Badge>
                  <Badge variant="secondary">
                    {results.chat_messages.length} Messages
                  </Badge>
                </div>
              </div>
            </div>

            {results.profile_status && (
              <div className="space-y-2">
                <h4 className="font-medium">Profile Status</h4>
                <div className="flex items-center space-x-2">
                  <Badge variant={results.profile_status.profile_exists ? "default" : "destructive"}>
                    {results.profile_status.profile_exists ? "Profile Found" : "Profile Not Found"}
                  </Badge>
                  {results.profile_status.profile_name && (
                    <span className="text-sm text-muted-foreground">
                      {results.profile_status.profile_name}
                    </span>
                  )}
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="tasks">
            <ScrollArea className="h-[400px]">
              <div className="space-y-3">
                {results.tasks.length === 0 ? (
                  <p className="text-muted-foreground text-center py-8">
                    No tasks found
                  </p>
                ) : (
                  results.tasks.map((task, index) => (
                    <Card key={index} className="p-3">
                      <div className="flex items-start space-x-3">
                        {task.icon_src && (
                          <img
                            src={task.icon_src}
                            alt="Task icon"
                            className="w-8 h-8 rounded"
                          />
                        )}
                        <div className="flex-1 space-y-1">
                          <h5 className="font-medium text-sm">
                            {task.title || 'Untitled Task'}
                          </h5>
                          {task.timestamp && (
                            <p className="text-xs text-muted-foreground">
                              {task.timestamp}
                            </p>
                          )}
                        </div>
                      </div>
                    </Card>
                  ))
                )}
              </div>
            </ScrollArea>
          </TabsContent>

          <TabsContent value="messages">
            <ScrollArea className="h-[400px]">
              <div className="space-y-3">
                {results.chat_messages.length === 0 ? (
                  <p className="text-muted-foreground text-center py-8">
                    No messages found
                  </p>
                ) : (
                  results.chat_messages.map((message, index) => (
                    <Card key={index} className="p-3">
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <Badge variant={message.sender === 'user' ? 'default' : 'secondary'}>
                            {message.sender}
                          </Badge>
                          {message.timestamp && (
                            <span className="text-xs text-muted-foreground">
                              {message.timestamp}
                            </span>
                          )}
                        </div>
                        <p className="text-sm">{message.content}</p>
                        {message.attachments && message.attachments.length > 0 && (
                          <div className="space-y-1">
                            <h6 className="text-xs font-medium">Attachments:</h6>
                            {message.attachments.map((attachment, attachIndex) => (
                              <div key={attachIndex} className="text-xs text-muted-foreground">
                                📎 {attachment.filename}
                                {attachment.details && ` - ${attachment.details}`}
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </Card>
                  ))
                )}
              </div>
            </ScrollArea>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
```

---

## 10. 🔧 Crawler Store Management

### Crawler Store

**`stores/crawler-store.ts`**
```typescript
import { create } from 'zustand';
import { crawlApi } from '@/lib/api';
import { useWebSocket } from '@/hooks/use-websocket';
import { CrawlRequest, CrawledData, WebSocketMessage } from '@/lib/types';

interface CrawlerState {
  // State
  isLoading: boolean;
  results: CrawledData | null;
  status: string | null;
  error: string | null;
  progress: number;
  currentRequestId: string | null;

  // Actions
  startCrawl: (request: CrawlRequest) => Promise<void>;
  clearResults: () => void;
  clearError: () => void;
  updateStatus: (status: string) => void;
  updateProgress: (progress: number) => void;
  setResults: (results: CrawledData) => void;
  setError: (error: string) => void;
}

export const useCrawlerStore = create<CrawlerState>((set, get) => ({
  // Initial state
  isLoading: false,
  results: null,
  status: null,
  error: null,
  progress: 0,
  currentRequestId: null,

  // Actions
  startCrawl: async (request: CrawlRequest) => {
    set({
      isLoading: true,
      error: null,
      status: 'Initializing crawl...',
      progress: 0,
      currentRequestId: request.request_id,
      results: null,
    });

    try {
      // Start the realtime crawl
      await crawlApi.crawlUrlRealtime(request);
      set({ status: 'Crawl started, waiting for updates...' });
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to start crawl',
        isLoading: false,
        status: null,
      });
    }
  },

  clearResults: () => {
    set({
      results: null,
      status: null,
      error: null,
      progress: 0,
      isLoading: false,
      currentRequestId: null,
    });
  },

  clearError: () => {
    set({ error: null });
  },

  updateStatus: (status: string) => {
    set({ status });
  },

  updateProgress: (progress: number) => {
    set({ progress });
  },

  setResults: (results: CrawledData) => {
    set({
      results,
      isLoading: false,
      status: 'Crawl completed successfully',
      progress: 100,
    });
  },

  setError: (error: string) => {
    set({
      error,
      isLoading: false,
      status: null,
    });
  },
}));

// Custom hook to integrate WebSocket with store
export function useCrawlerWithWebSocket() {
  const store = useCrawlerStore();
  const { lastMessage, isConnected, error: wsError } = useWebSocket(store.currentRequestId);

  // Handle WebSocket messages
  React.useEffect(() => {
    if (!lastMessage) return;

    switch (lastMessage.type) {
      case 'progress':
        store.updateStatus(lastMessage.message);
        break;
      case 'data':
        store.setResults(lastMessage.data);
        break;
      case 'error':
        store.setError(lastMessage.message);
        break;
    }
  }, [lastMessage, store]);

  // Handle WebSocket errors
  React.useEffect(() => {
    if (wsError) {
      store.setError('WebSocket connection error');
    }
  }, [wsError, store]);

  return {
    ...store,
    isWebSocketConnected: isConnected,
    webSocketError: wsError,
  };
}
```

---

## 11. 🔐 Admin Dashboard Implementation

### Admin Main Page

**`app/admin/page.tsx`**
```typescript
'use client';

import { useEffect } from 'react';
import { AdminLogin } from '@/components/admin/admin-login';
import { AdminDashboard } from '@/components/admin/admin-dashboard';
import { useAdminStore } from '@/stores/admin-store';

export default function AdminPage() {
  const { apiKey, loadProfiles } = useAdminStore();

  useEffect(() => {
    if (apiKey) {
      loadProfiles();
    }
  }, [apiKey, loadProfiles]);

  if (!apiKey) {
    return <AdminLogin />;
  }

  return <AdminDashboard />;
}
```

### Admin Login Component

**`components/admin/admin-login.tsx`**
```typescript
'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAdminStore } from '@/stores/admin-store';
import { Shield, Eye, EyeOff } from 'lucide-react';

const loginSchema = z.object({
  apiKey: z.string().min(1, 'API Key is required'),
});

type LoginForm = z.infer<typeof loginSchema>;

export function AdminLogin() {
  const [showApiKey, setShowApiKey] = useState(false);
  const { setApiKey, error, clearError } = useAdminStore();

  const form = useForm<LoginForm>({
    resolver: zodResolver(loginSchema),
  });

  const onSubmit = (data: LoginForm) => {
    clearError();
    setApiKey(data.apiKey);
  };

  return (
    <div className="min-h-[60vh] flex items-center justify-center">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="flex items-center justify-center space-x-2">
            <Shield className="h-6 w-6" />
            <span>Admin Access</span>
          </CardTitle>
          <p className="text-muted-foreground">
            Enter your API key to access the admin dashboard
          </p>
        </CardHeader>
        <CardContent>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="apiKey">API Key</Label>
              <div className="relative">
                <Input
                  id="apiKey"
                  type={showApiKey ? 'text' : 'password'}
                  placeholder="your_super_secret_key_here"
                  {...form.register('apiKey')}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowApiKey(!showApiKey)}
                >
                  {showApiKey ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
              {form.formState.errors.apiKey && (
                <p className="text-sm text-destructive">
                  {form.formState.errors.apiKey.message}
                </p>
              )}
            </div>

            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <Button type="submit" className="w-full">
              <Shield className="mr-2 h-4 w-4" />
              Access Admin Dashboard
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
```

### Admin Dashboard Component

**`components/admin/admin-dashboard.tsx`**
```typescript
'use client';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ProfileList } from './profile-list';
import { ProfileSetup } from './profile-setup';
import { useAdminStore } from '@/stores/admin-store';
import { Settings, LogOut, RefreshCw } from 'lucide-react';

export function AdminDashboard() {
  const { profiles, isLoading, loadProfiles, setApiKey } = useAdminStore();

  const handleLogout = () => {
    setApiKey('');
  };

  const handleRefresh = () => {
    loadProfiles();
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Admin Dashboard</h1>
          <p className="text-muted-foreground">
            Manage Chrome profiles and crawler settings
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={handleRefresh} disabled={isLoading}>
            <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline" onClick={handleLogout}>
            <LogOut className="mr-2 h-4 w-4" />
            Logout
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Statistics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Total Profiles:</span>
                <span className="font-medium">{profiles.length}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Status:</span>
                <span className="font-medium text-green-600">Active</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="md:col-span-2">
          <ProfileSetup />
        </div>
      </div>

      <ProfileList />
    </div>
  );
}
```

### Profile Setup Component

**`components/admin/profile-setup.tsx`**
```typescript
'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { SetupProfileRequestSchema, type SetupProfileRequest } from '@/lib/types';
import { useAdminStore } from '@/stores/admin-store';
import { Plus, Loader2, AlertCircle } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

export function ProfileSetup() {
  const { setupProfile, isLoading, error } = useAdminStore();
  const { toast } = useToast();

  const form = useForm<SetupProfileRequest>({
    resolver: zodResolver(SetupProfileRequestSchema),
    defaultValues: {
      url: 'https://manus.im/',
    },
  });

  const onSubmit = async (data: SetupProfileRequest) => {
    const success = await setupProfile(data);
    if (success) {
      toast({
        title: 'Profile Setup Started',
        description: `Profile "${data.profile_name}" setup initiated. Please complete login in the browser window.`,
      });
      form.reset({ url: 'https://manus.im/' });
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Plus className="h-5 w-5" />
          <span>Setup New Profile</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="profile_name">Profile Name</Label>
              <Input
                id="profile_name"
                placeholder="my_manus_profile"
                {...form.register('profile_name')}
              />
              {form.formState.errors.profile_name && (
                <p className="text-sm text-destructive">
                  {form.formState.errors.profile_name.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="url">URL</Label>
              <Input
                id="url"
                placeholder="https://manus.im/"
                {...form.register('url')}
              />
              {form.formState.errors.url && (
                <p className="text-sm text-destructive">
                  {form.formState.errors.url.message}
                </p>
              )}
            </div>
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              This will open a browser window where you need to manually log in to Manus.im.
              The session will be saved to the profile for future use.
            </AlertDescription>
          </Alert>

          <Button type="submit" disabled={isLoading} className="w-full">
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Setting up profile...
              </>
            ) : (
              <>
                <Plus className="mr-2 h-4 w-4" />
                Setup Profile
              </>
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
```

### Profile List Component

**`components/admin/profile-list.tsx`**
```typescript
'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { useAdminStore } from '@/stores/admin-store';
import { Trash2, User, AlertTriangle } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

export function ProfileList() {
  const { profiles, deleteProfile, isLoading } = useAdminStore();
  const { toast } = useToast();
  const [deletingProfile, setDeletingProfile] = useState<string | null>(null);

  const handleDeleteProfile = async (profileName: string) => {
    setDeletingProfile(profileName);
    const success = await deleteProfile(profileName);
    setDeletingProfile(null);

    if (success) {
      toast({
        title: 'Profile Deleted',
        description: `Profile "${profileName}" has been deleted successfully.`,
      });
    }
  };

  if (profiles.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <User className="h-5 w-5" />
            <span>Chrome Profiles</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              No profiles found. Create a new profile to get started.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <User className="h-5 w-5" />
            <span>Chrome Profiles</span>
          </div>
          <Badge variant="secondary">{profiles.length} profiles</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {profiles.map((profile) => (
            <Card key={profile} className="relative">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <h4 className="font-medium truncate">{profile}</h4>
                    <p className="text-sm text-muted-foreground">
                      Chrome Profile
                    </p>
                  </div>

                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        disabled={deletingProfile === profile}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Delete Profile</AlertDialogTitle>
                        <AlertDialogDescription>
                          Are you sure you want to delete the profile "{profile}"?
                          This action cannot be undone and will remove all saved session data.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={() => handleDeleteProfile(profile)}
                          className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                        >
                          Delete Profile
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
```

---

## 12. 🏠 Home Page Implementation

### Home Page

**`app/page.tsx`**
```typescript
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Spider, Settings, Play, Shield, Zap, Globe } from 'lucide-react';

export default function HomePage() {
  const features = [
    {
      icon: <Spider className="h-8 w-8" />,
      title: 'Advanced Web Crawling',
      description: 'Powerful Playwright-based crawler with Chrome profile support for authenticated sessions.',
    },
    {
      icon: <Zap className="h-8 w-8" />,
      title: 'Realtime Updates',
      description: 'Live progress tracking with WebSocket integration for instant feedback.',
    },
    {
      icon: <Shield className="h-8 w-8" />,
      title: 'Secure Admin Panel',
      description: 'Protected admin interface for managing Chrome profiles and crawler settings.',
    },
    {
      icon: <Globe className="h-8 w-8" />,
      title: 'Modern UI/UX',
      description: 'Beautiful, responsive interface built with NextJS 14 and Tailwind CSS.',
    },
  ];

  return (
    <div className="space-y-12">
      {/* Hero Section */}
      <div className="text-center space-y-6">
        <div className="space-y-4">
          <Badge variant="secondary" className="px-4 py-2">
            NextJS Frontend for Manus Crawler
          </Badge>
          <h1 className="text-4xl md:text-6xl font-bold tracking-tight">
            Modern Web Crawler
            <span className="text-primary block">Interface</span>
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Professional frontend interface for the Manus.im crawler system with
            realtime updates, admin dashboard, and seamless backend integration.
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button asChild size="lg">
            <Link href="/crawler">
              <Play className="mr-2 h-5 w-5" />
              Start Crawling
            </Link>
          </Button>
          <Button variant="outline" size="lg" asChild>
            <Link href="/admin">
              <Settings className="mr-2 h-5 w-5" />
              Admin Dashboard
            </Link>
          </Button>
        </div>
      </div>

      {/* Features Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {features.map((feature, index) => (
          <Card key={index} className="relative overflow-hidden">
            <CardHeader>
              <CardTitle className="flex items-center space-x-3">
                <div className="text-primary">{feature.icon}</div>
                <span>{feature.title}</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">{feature.description}</p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Quick Start Section */}
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl">Quick Start Guide</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-bold">
                  1
                </div>
                <h3 className="font-semibold">Setup Profile</h3>
              </div>
              <p className="text-sm text-muted-foreground">
                Go to Admin Dashboard and create a Chrome profile for authenticated crawling.
              </p>
              <Button variant="outline" size="sm" asChild>
                <Link href="/admin">Setup Profile</Link>
              </Button>
            </div>

            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-bold">
                  2
                </div>
                <h3 className="font-semibold">Configure Crawl</h3>
              </div>
              <p className="text-sm text-muted-foreground">
                Enter the Manus.im URL and select your profile for crawling.
              </p>
              <Button variant="outline" size="sm" asChild>
                <Link href="/crawler">Start Crawler</Link>
              </Button>
            </div>

            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-bold">
                  3
                </div>
                <h3 className="font-semibold">View Results</h3>
              </div>
              <p className="text-sm text-muted-foreground">
                Watch realtime progress and download crawled data in JSON format.
              </p>
              <Button variant="outline" size="sm" disabled>
                Results Available After Crawl
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* System Status */}
      <Card>
        <CardHeader>
          <CardTitle>System Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <h4 className="font-medium">Frontend Stack</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Framework:</span>
                  <Badge variant="secondary">NextJS 14</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Styling:</span>
                  <Badge variant="secondary">Tailwind CSS</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">UI Components:</span>
                  <Badge variant="secondary">shadcn/ui</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">State Management:</span>
                  <Badge variant="secondary">Zustand</Badge>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <h4 className="font-medium">Backend Integration</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">API:</span>
                  <Badge variant="secondary">FastAPI</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Realtime:</span>
                  <Badge variant="secondary">WebSocket</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Crawler:</span>
                  <Badge variant="secondary">Playwright</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Type Safety:</span>
                  <Badge variant="secondary">TypeScript + Zod</Badge>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
```

---

## 13. 🚀 Deployment & Production Setup

### Development Setup

**1. Install Dependencies**
```bash
# Create NextJS project
npx create-next-app@latest frontend --typescript --tailwind --eslint --app --src-dir=false --import-alias="@/*"

cd frontend

# Install additional dependencies
npm install @radix-ui/react-alert-dialog @radix-ui/react-badge @radix-ui/react-button @radix-ui/react-card @radix-ui/react-checkbox @radix-ui/react-input @radix-ui/react-label @radix-ui/react-progress @radix-ui/react-scroll-area @radix-ui/react-select @radix-ui/react-tabs @radix-ui/react-toast class-variance-authority clsx tailwind-merge lucide-react zod zustand axios react-hook-form @hookform/resolvers uuid

# Install dev dependencies
npm install -D @types/uuid
```

**2. Setup shadcn/ui**
```bash
# Initialize shadcn/ui
npx shadcn-ui@latest init

# Add required components
npx shadcn-ui@latest add button card input label badge alert tabs progress scroll-area select checkbox toast alert-dialog
```

**3. Environment Configuration**
```bash
# Create environment file
cp .env.example .env.local

# Edit .env.local with your backend URL
BACKEND_URL=http://localhost:8000
WS_URL=ws://localhost:8000
ADMIN_API_KEY=your_super_secret_key_here
```

**4. Start Development Server**
```bash
npm run dev
```

### Production Deployment

**1. Docker Setup**

**`Dockerfile`**
```dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json* ./
RUN npm ci

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

ENV NEXT_TELEMETRY_DISABLED 1

RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Set the correct permission for prerender cache
RUN mkdir .next
RUN chown nextjs:nodejs .next

# Automatically leverage output traces to reduce image size
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

**`docker-compose.yml`**
```yaml
version: '3.8'

services:
  frontend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: manus_crawler_frontend
    ports:
      - "3000:3000"
    environment:
      BACKEND_URL: http://backend:8000
      WS_URL: ws://backend:8000
      ADMIN_API_KEY: your_super_secret_key_here
    depends_on:
      - backend
    networks:
      - manus_network

  backend:
    build:
      context: ../
      dockerfile: docker/Dockerfile
    container_name: manus_crawler_backend
    ports:
      - "8000:8000"
    volumes:
      - chrome_profiles_data:/app/data/chrome_profiles
    environment:
      ADMIN_API_KEY: your_super_secret_key_here
      CHROME_PROFILE_BASE_PATH: /app/data/chrome_profiles
    networks:
      - manus_network

volumes:
  chrome_profiles_data:

networks:
  manus_network:
    driver: bridge
```

**2. Build and Deploy**
```bash
# Build and start all services
docker-compose up --build -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### Production Environment Variables

**`.env.production`**
```env
BACKEND_URL=https://your-backend-domain.com
WS_URL=wss://your-backend-domain.com
ADMIN_API_KEY=your_production_secret_key
```

### Nginx Configuration (Optional)

**`nginx.conf`**
```nginx
server {
    listen 80;
    server_name your-domain.com;

    # Frontend
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Backend API
    location /api/ {
        proxy_pass http://localhost:8000/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # WebSocket
    location /ws/ {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

---

## 🎉 **Kết luận**

Frontend NextJS đã được thiết kế hoàn chỉnh với:

✅ **Modern Architecture** - NextJS 14 App Router với TypeScript
✅ **Beautiful UI** - shadcn/ui components với Tailwind CSS
✅ **Type Safety** - Zod validation và TypeScript types
✅ **Realtime Integration** - WebSocket với custom hooks
✅ **Admin Dashboard** - Secure profile management
✅ **Production Ready** - Docker deployment và Nginx config
✅ **Developer Experience** - ESLint, Prettier, và hot reload

**Sẵn sàng cho production deployment!** 🚀

### 📋 Checklist triển khai:

- [ ] Setup backend FastAPI server
- [ ] Install và configure NextJS frontend
- [ ] Setup environment variables
- [ ] Test API integration
- [ ] Configure WebSocket connections
- [ ] Setup admin authentication
- [ ] Test Chrome profile management
- [ ] Deploy với Docker
- [ ] Configure reverse proxy (Nginx)
- [ ] Setup SSL certificates
- [ ] Monitor và logging

**Frontend hoàn chỉnh và tích hợp hoàn hảo với Manus Crawler Backend!** 🎯

---

## 🎨 **Manus-style UI Summary**

### ✅ **UI Components đã được thiết kế giống Manus.im:**

**🔧 Layout Structure:**
- ✅ **Sidebar Navigation** - 300px width, collapsible, dark theme
- ✅ **Task List** - Với icons, titles, timestamps, và preview text
- ✅ **Main Content Area** - Chat interface với header và input area
- ✅ **Message Display** - User/Manus messages với attachments support

**🎨 Visual Design:**
- ✅ **Dark Theme** - Matching Manus.im color scheme
- ✅ **CSS Variables** - Using exact Manus variable names
- ✅ **Hover Effects** - Subtle transitions và hover states
- ✅ **Typography** - Consistent font sizes và weights
- ✅ **Spacing** - Exact padding và margins như Manus

**⚡ Interactive Elements:**
- ✅ **Collapsible Sidebar** - Toggle với smooth animation
- ✅ **Task Selection** - Click để switch between tasks
- ✅ **Message Input** - Textarea với Enter to send
- ✅ **Loading States** - Spinner và disabled states
- ✅ **Keyboard Shortcuts** - Cmd+K for new task

### 🚀 **Quick Start với Manus-style UI:**

**1. Setup Project:**
```bash
npx create-next-app@latest manus-crawler-frontend --typescript --tailwind --eslint --app
cd manus-crawler-frontend
npm install @radix-ui/react-* lucide-react zod zustand axios
```

**2. Copy Manus Components:**
```bash
# Copy components từ FrontendRequired.md:
# - components/manus/sidebar.tsx
# - components/manus/main-content.tsx
# - app/globals.css (với Manus variables)
# - tailwind.config.js (với Manus colors)
```

**3. Use trong Page:**
```typescript
import { ManusStyleSidebar } from '@/components/manus/sidebar';
import { ManusStyleMainContent } from '@/components/manus/main-content';

export default function Page() {
  return (
    <div className="flex w-full h-screen overflow-hidden">
      <ManusStyleSidebar tasks={tasks} onTaskSelect={handleTaskSelect} />
      <ManusStyleMainContent messages={messages} onSendMessage={handleSend} />
    </div>
  );
}
```

### 📱 **Responsive Design:**
- ✅ **Mobile-first** - Sidebar collapses on mobile
- ✅ **Touch Support** - Touch-friendly hover states
- ✅ **Flexible Layout** - Adapts to different screen sizes

### 🔧 **Customization:**
- ✅ **CSS Variables** - Easy theme customization
- ✅ **Component Props** - Flexible component API
- ✅ **TypeScript** - Full type safety
- ✅ **Modular Design** - Easy to extend và modify

**Perfect Manus.im clone với modern NextJS architecture!** ⚡

---

## 💬 **Interactive Chat Crawling Integration**

### 🎯 **Tính năng mới: Chat trực tiếp với Manus.im**

Tích hợp chức năng **interactive chat crawling** - user có thể chat trực tiếp với Manus.im qua frontend và nhận response realtime.

### 🔧 **Implementation Components:**

#### Chat Store Management

**`stores/chat-store.ts`**
```typescript
import { create } from 'zustand';
import { crawlApi } from '@/lib/api';
import { ChatWithManusRequest, InteractiveChatResult } from '@/lib/types';

interface ChatState {
  // State
  isLoading: boolean;
  currentTaskUrl: string | null;
  chatHistory: InteractiveChatResult[];
  error: string | null;
  currentRequestId: string | null;

  // Actions
  startChat: (request: ChatWithManusRequest) => Promise<void>;
  clearHistory: () => void;
  clearError: () => void;
  setTaskUrl: (url: string) => void;
  addChatResult: (result: InteractiveChatResult) => void;
  setError: (error: string) => void;
}

export const useChatStore = create<ChatState>((set, get) => ({
  // Initial state
  isLoading: false,
  currentTaskUrl: null,
  chatHistory: [],
  error: null,
  currentRequestId: null,

  // Actions
  startChat: async (request: ChatWithManusRequest) => {
    set({
      isLoading: true,
      error: null,
      currentRequestId: request.request_id,
    });

    try {
      await crawlApi.chatWithManusRealtime(request);
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to start chat',
        isLoading: false,
      });
    }
  },

  clearHistory: () => {
    set({
      chatHistory: [],
      error: null,
      isLoading: false,
      currentRequestId: null,
    });
  },

  clearError: () => {
    set({ error: null });
  },

  setTaskUrl: (url: string) => {
    set({ currentTaskUrl: url });
  },

  addChatResult: (result: InteractiveChatResult) => {
    set((state) => ({
      chatHistory: [...state.chatHistory, result],
      isLoading: false,
    }));
  },

  setError: (error: string) => {
    set({
      error,
      isLoading: false,
    });
  },
}));
```

#### Interactive Chat Component

**`components/chat/interactive-chat.tsx`**
```typescript
'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { v4 as uuidv4 } from 'uuid';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { ChatWithManusRequestSchema, type ChatWithManusRequest } from '@/lib/types';
import { useChatStore } from '@/stores/chat-store';
import { useWebSocket } from '@/hooks/use-websocket';
import { MessageSquare, Send, Loader2, Clock, CheckCircle } from 'lucide-react';

export function InteractiveChat() {
  const {
    startChat,
    chatHistory,
    isLoading,
    error,
    currentRequestId,
    clearError,
    addChatResult,
    setError
  } = useChatStore();

  const { lastMessage } = useWebSocket(currentRequestId);

  const form = useForm<ChatWithManusRequest>({
    resolver: zodResolver(ChatWithManusRequestSchema),
    defaultValues: {
      task_url: 'https://manus.im/app/',
      headless: true,
      request_id: uuidv4(),
    },
  });

  // Handle WebSocket messages
  React.useEffect(() => {
    if (!lastMessage) return;

    switch (lastMessage.type) {
      case 'data':
        if ('chat_response' in lastMessage.data) {
          addChatResult(lastMessage.data);
        }
        break;
      case 'error':
        setError(lastMessage.message);
        break;
    }
  }, [lastMessage, addChatResult, setError]);

  const onSubmit = async (data: ChatWithManusRequest) => {
    // Generate new request ID for each chat
    const requestWithId = {
      ...data,
      request_id: uuidv4(),
    };

    form.setValue('request_id', requestWithId.request_id);
    await startChat(requestWithId);
  };

  return (
    <div className="space-y-6">
      {/* Chat Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <MessageSquare className="h-5 w-5" />
            <span>Interactive Chat with Manus</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="task_url">Manus Task URL</Label>
              <Input
                id="task_url"
                placeholder="https://manus.im/app/your-task-id"
                {...form.register('task_url')}
              />
              {form.formState.errors.task_url && (
                <p className="text-sm text-destructive">
                  {form.formState.errors.task_url.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="message">Message to send</Label>
              <Textarea
                id="message"
                placeholder="Type your message to Manus here..."
                rows={3}
                {...form.register('message')}
              />
              {form.formState.errors.message && (
                <p className="text-sm text-destructive">
                  {form.formState.errors.message.message}
                </p>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="profile_name">Chrome Profile (optional)</Label>
                <Input
                  id="profile_name"
                  placeholder="my_manus_profile"
                  {...form.register('profile_name')}
                />
              </div>

              <div className="flex items-center space-x-2 pt-6">
                <input
                  type="checkbox"
                  id="headless"
                  {...form.register('headless')}
                  className="rounded"
                />
                <Label htmlFor="headless">Run in headless mode</Label>
              </div>
            </div>

            {error && (
              <div className="p-3 rounded-lg bg-destructive/10 text-destructive">
                {error}
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={clearError}
                  className="ml-2"
                >
                  ✕
                </Button>
              </div>
            )}

            <Button type="submit" disabled={isLoading} className="w-full">
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Sending message...
                </>
              ) : (
                <>
                  <Send className="mr-2 h-4 w-4" />
                  Send Message to Manus
                </>
              )}
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Chat History */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <MessageSquare className="h-5 w-5" />
              <span>Chat History</span>
            </div>
            <Badge variant="secondary">{chatHistory.length} conversations</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[400px]">
            {chatHistory.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No chat history yet. Start a conversation with Manus!
              </div>
            ) : (
              <div className="space-y-4">
                {chatHistory.map((chat, index) => (
                  <ChatHistoryItem key={index} chat={chat} />
                ))}
              </div>
            )}
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );
}

interface ChatHistoryItemProps {
  chat: InteractiveChatResult;
}

function ChatHistoryItem({ chat }: ChatHistoryItemProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  if (!chat.success || !chat.chat_response) {
    return (
      <Card className="border-destructive/20">
        <CardContent className="p-4">
          <div className="flex items-center space-x-2 text-destructive">
            <Clock className="h-4 w-4" />
            <span className="font-medium">Failed</span>
            <span className="text-sm">
              {new Date(chat.timestamp).toLocaleTimeString()}
            </span>
          </div>
          <p className="text-sm text-muted-foreground mt-2">
            {chat.error || 'Unknown error occurred'}
          </p>
        </CardContent>
      </Card>
    );
  }

  const { chat_response } = chat;

  return (
    <Card>
      <CardContent className="p-4">
        <div className="space-y-3">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span className="font-medium">Chat Completed</span>
            </div>
            <span className="text-sm text-muted-foreground">
              {new Date(chat_response.timestamp).toLocaleTimeString()}
            </span>
          </div>

          {/* User Message */}
          <div className="bg-primary/10 rounded-lg p-3">
            <div className="text-sm font-medium text-primary mb-1">You:</div>
            <div className="text-sm">{chat_response.user_message}</div>
          </div>

          {/* Manus Response */}
          <div className="bg-muted rounded-lg p-3">
            <div className="text-sm font-medium mb-1">Manus:</div>
            <div className="text-sm whitespace-pre-wrap">
              {chat_response.manus_response}
            </div>

            {/* Attachments */}
            {chat_response.attachments.length > 0 && (
              <div className="mt-3 space-y-2">
                <div className="text-xs font-medium text-muted-foreground">
                  Attachments:
                </div>
                {chat_response.attachments.map((attachment, index) => (
                  <div
                    key={index}
                    className="bg-background rounded border p-2 text-xs"
                  >
                    <div className="font-medium">{attachment.filename}</div>
                    {attachment.details && (
                      <div className="text-muted-foreground">
                        {attachment.details}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Updated Page Data */}
          {chat.updated_page_data && (
            <div className="pt-2 border-t">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsExpanded(!isExpanded)}
                className="text-xs"
              >
                {isExpanded ? 'Hide' : 'Show'} Updated Page Data
              </Button>

              {isExpanded && (
                <div className="mt-2 p-3 bg-muted rounded text-xs">
                  <div className="space-y-2">
                    <div>
                      <span className="font-medium">Page Title:</span>{' '}
                      {chat.updated_page_data.page_title}
                    </div>
                    <div>
                      <span className="font-medium">Tasks:</span>{' '}
                      {chat.updated_page_data.tasks.length} items
                    </div>
                    <div>
                      <span className="font-medium">Messages:</span>{' '}
                      {chat.updated_page_data.chat_messages.length} items
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
```

### 🎯 **Usage trong Manus-style Layout:**

**Tích hợp vào `app/crawler/page.tsx`:**
```typescript
'use client';

import { useState } from 'react';
import { ManusStyleSidebar } from '@/components/manus/sidebar';
import { ManusStyleMainContent } from '@/components/manus/main-content';
import { InteractiveChat } from '@/components/chat/interactive-chat';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useCrawlerStore } from '@/stores/crawler-store';
import { TaskItem, ChatMessage } from '@/lib/types';

export default function CrawlerPage() {
  const { results, isLoading } = useCrawlerStore();
  const [selectedTask, setSelectedTask] = useState<TaskItem | null>(null);

  const tasks: TaskItem[] = results?.tasks || [];
  const messages: ChatMessage[] = results?.chat_messages || [];

  return (
    <div className="flex w-full h-screen overflow-hidden">
      {/* Sidebar */}
      <ManusStyleSidebar
        tasks={tasks}
        onTaskSelect={setSelectedTask}
        onNewTask={() => console.log('New task')}
      />

      {/* Main Content with Tabs */}
      <div className="flex-1 flex flex-col">
        <Tabs defaultValue="crawl" className="flex-1 flex flex-col">
          <div className="border-b px-6 py-2">
            <TabsList>
              <TabsTrigger value="crawl">Crawl Data</TabsTrigger>
              <TabsTrigger value="chat">Interactive Chat</TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="crawl" className="flex-1 flex flex-col">
            <ManusStyleMainContent
              title={selectedTask?.title || "Manus Crawler"}
              messages={messages}
              isLoading={isLoading}
            />
          </TabsContent>

          <TabsContent value="chat" className="flex-1 overflow-auto p-6">
            <InteractiveChat />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
```

### ✅ **Benefits của Interactive Chat:**

- ✅ **Real Chat Experience** - Chat trực tiếp với Manus như user thật
- ✅ **Realtime Response** - Nhận response ngay lập tức qua WebSocket
- ✅ **Chat History** - Lưu trữ và hiển thị lịch sử chat
- ✅ **Attachment Support** - Hiển thị file attachments từ Manus
- ✅ **Updated Page Data** - Crawl toàn bộ page data sau mỗi chat
- ✅ **Error Handling** - Xử lý timeout và lỗi login
- ✅ **Manus-style UI** - Tích hợp hoàn hảo với Manus design

**Perfect Interactive Chat Integration!** 💬✨
