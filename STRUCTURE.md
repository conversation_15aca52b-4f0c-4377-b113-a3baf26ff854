# 📁 Youhome-2 Project Structure

## 🎯 Tối gọn và tập trung vào core functionality

```
Youhome-2/
├── 📄 README.md              # Documentation chính
├── 📄 Required.md            # Requirements từ user
├── 📄 setup.sh               # Setup script đơn giản
├── 📄 run.py                 # Entry point
├── 📄 demo.py                # Demo script
├── 📄 requirements.txt       # Python dependencies
│
├── 📂 app/                   # Core application
│   ├── 📄 main.py           # FastAPI app
│   ├── 📂 api/              # API endpoints
│   │   ├── 📄 endpoints.py  # REST API
│   │   └── 📄 websocket.py  # WebSocket
│   ├── 📂 core/             # Core logic
│   │   ├── 📄 config.py     # Configuration
│   │   ├── 📄 crawler.py    # Web scraping logic
│   │   ├── 📄 message_classifier.py  # Message classification
│   │   ├── 📄 selectors.py  # CSS selectors
│   │   └── 📄 selectors.json # Selector definitions
│   ├── 📂 models/           # Data models
│   │   └── 📄 schemas.py    # Pydantic schemas
│   └── 📂 static/           # Static files
│       ├── 📂 css/          # Stylesheets
│       └── 📂 templates/    # HTML templates
│
├── 📂 tests/                # Essential tests only
│   ├── 📄 test_api.py       # API tests
│   ├── 📄 test_app.py       # App tests
│   └── 📄 test_websocket.py # WebSocket tests
│
├── 📂 data/                 # Data storage
│   ├── 📂 chrome_profiles/  # Chrome profiles (empty)
│   └── 📄 html-manus.example.html # Example HTML
│
├── 📂 docker/               # Docker configuration
│   ├── 📄 Dockerfile        # Docker image
│   └── 📄 docker-compose.yml # Docker compose
│
└── 📂 venv/                 # Virtual environment
```

## ✅ Những gì đã được giữ lại

### 🔧 Core Application
- **FastAPI server** với REST API và WebSocket
- **Web crawler** với Playwright
- **Message classification** tự động
- **Chrome profile management**
- **CSS selectors** có thể cấu hình

### 📊 Essential Features
- **Message type classification**: text, code, list, file, mixed
- **Content analysis**: code blocks, languages, file references
- **Real-time updates** qua WebSocket
- **Docker support** cho deployment

### 🧪 Core Tests
- API endpoint tests
- Application functionality tests  
- WebSocket communication tests

### 📚 Documentation
- **README.md**: Complete guide với quick start
- **STRUCTURE.md**: Project structure overview
- **Required.md**: Original requirements

## 🗑️ Những gì đã được xóa

### ❌ Removed Files/Folders
- `docs/` folder với 5+ documentation files
- `scripts/` folder với 7+ demo/test scripts
- Duplicate `scripts/venv/` virtual environment
- Test data files: `*.json` demo responses
- Chrome profile test data
- Redundant test files (5+ test scripts)

### 📉 Complexity Reduction
- **From 15+ docs** → **3 essential docs**
- **From 10+ scripts** → **2 essential scripts** (setup.sh, demo.py)
- **From 8+ test files** → **3 core tests**
- **From 50+ files** → **~25 essential files**

## 🚀 Benefits

### ✅ Simplified Structure
- **Easy to navigate** - clear folder hierarchy
- **Focused functionality** - only essential features
- **Reduced complexity** - fewer files to maintain
- **Better organization** - logical grouping

### 🎯 Developer Experience
- **Quick setup** with single script
- **Simple demo** to test functionality
- **Clear documentation** in README
- **Essential tests** for core features

### 📦 Deployment Ready
- **Docker support** maintained
- **Production configuration** preserved
- **Virtual environment** clean
- **Dependencies** optimized

## 🔧 Usage

### Quick Start
```bash
# Setup
./setup.sh

# Run
python run.py

# Demo
python demo.py

# Test
python tests/test_api.py
```

### Development
```bash
# Activate environment
source venv/bin/activate

# Install new dependencies
pip install package_name
pip freeze > requirements.txt

# Run with auto-reload
uvicorn app.main:app --reload
```

### Docker
```bash
cd docker
docker-compose up --build
```

## 📈 Next Steps

### Potential Additions (if needed)
- **Logging configuration** (single file)
- **Database integration** (if required)
- **Authentication** (if needed)
- **Rate limiting** (if required)

### Maintenance
- Keep structure simple
- Add files only when necessary
- Document new features in README
- Maintain test coverage for core features

---

**Philosophy**: "Simplicity is the ultimate sophistication" - Keep only what's essential, make it work perfectly.
