#!/usr/bin/env python3
"""
Demo script để test Youhome-2 Web Scraper
"""

import asyncio
import json
from pathlib import Path
from app.core.crawler import crawl_manus_page_content


async def demo_crawl():
    """Demo crawl với HTML example."""
    print("🚀 Demo Youhome-2 Web Scraper")
    print("=" * 40)
    
    # Đọc HTML example
    html_file = Path("data/html-manus.example.html")
    
    if not html_file.exists():
        print("❌ File html-manus.example.html không tồn tại!")
        print("   Vui lòng thêm file HTML để test.")
        return
    
    with open(html_file, 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    print(f"📄 Đang crawl file: {html_file.name}")
    print(f"📊 File size: {len(html_content):,} characters")
    
    try:
        # Crawl HTML
        result = await crawl_manus_page_content(
            html_content=html_content,
            headless=True
        )
        
        if not result["success"]:
            print(f"❌ Crawl failed: {result.get('message', 'Unknown error')}")
            return
        
        print(f"✅ Crawl successful!")
        
        # Hi<PERSON>n thị kết quả
        data = result["data"]
        chat_messages = data.get("chat_messages", [])
        
        print(f"\n📊 Results:")
        print(f"   - Page title: {data.get('page_title', 'N/A')}")
        print(f"   - Tasks found: {len(data.get('tasks', []))}")
        print(f"   - Chat messages: {len(chat_messages)}")
        
        # Phân tích messages
        user_count = 0
        manus_count = 0
        message_types = {}
        
        for msg in chat_messages:
            if msg.get("type") == "user":
                user_count += 1
            elif msg.get("type") == "manus":
                manus_count += 1
                subtype = msg.get("message_subtype", "unknown")
                message_types[subtype] = message_types.get(subtype, 0) + 1
        
        print(f"\n💬 Message Analysis:")
        print(f"   👤 User messages: {user_count}")
        print(f"   🤖 Manus messages: {manus_count}")
        
        if message_types:
            print(f"   🏷️  Message types:")
            for msg_type, count in message_types.items():
                print(f"      - {msg_type}: {count}")
        
        # Lưu kết quả
        output_file = Path("data/demo_result.json")
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        print(f"\n📁 Results saved to: {output_file}")
        print(f"🎉 Demo completed successfully!")
        
    except Exception as e:
        print(f"❌ Demo failed: {str(e)}")


def main():
    """Main function."""
    asyncio.run(demo_crawl())


if __name__ == "__main__":
    main()
